package com.wxy.system.server.service.university.students;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wxy.system.common.dto.university.study.StudyCenterDTO;
import com.wxy.system.common.dto.university.study.StudyIncompleteVo;
import com.wxy.system.common.dto.university.study.StudyTaskDTO;
import com.wxy.system.common.dto.university.study.UniversityTaskSubmitDTO;
import com.wxy.system.common.vo.university.course.UniversityStuCourseListVO;
import com.wxy.system.common.vo.university.task.UniversityChildTaskVO;
import com.wxy.system.common.vo.university.userdet.UniversityUserTaskDetUpdateVO;
import com.wxy.system.common.vo.university.userdet.UserTaskStructureVO;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;

import java.util.List;

/**
* 课程学员任务明细 服务类
*
* <AUTHOR>
* @since 2024-09-13
*/
public interface IUniversityUserTaskDetService extends IService<UniversityCourseParticipantTaskDet> {
    /**
    * 新增 课程学员任务明细
    */
    UniversityCourseParticipantTaskDet add(UniversityCourseParticipantTaskDet dto);

    /**
    * 修改 课程学员任务明细
    */
    UniversityCourseParticipantTaskDet edit(UniversityCourseParticipantTaskDet dto);

    /**
    * 删除 课程学员任务明细
    */
    void deleteById(String id);

    /**
    * 根据id获取 课程学员任务明细 详情
    */
    UniversityCourseParticipantTaskDet queryById(String id);


    /**
    * 分页查询 课程学员任务明细
    */
    IPage<UniversityCourseParticipantTaskDet> findPage(IPage<UniversityCourseParticipantTaskDet> page, UniversityCourseParticipantTaskDet dto);

    /**
    * 根据id获取 课程学员任务明细 实体
    */
    UniversityCourseParticipantTaskDet checkEntity(String id);

    /**
     * 根据学员用户id 查询未完成任务
     * @param sysUserId
     * @return
     */
    List<UniversityChildTaskVO> findUnfinshTsak(StudyCenterDTO dto);

    /**
     * 根据学员用户id 查询课程
     * @param dto
     * @return
     */
    List<UniversityStuCourseListVO> findCourseByUserId(StudyCenterDTO dto);


    /**
     * 批量查询学员任务信息
     * @param sysUserId
     * @param taskDetIds
     * @param projectId
     * @return
     */
    List<UniversityChildTaskVO> findTsakList(String sysUserId, List<String> taskDetIds, String projectId);

    /**
     * 任务进度更新
     * @param dto
     */
    void taskUpdateProgressAll(UniversityUserTaskDetUpdateVO dto);


    /**
     * 获取任务结构
     * @return
     * <AUTHOR>
     */
    UserTaskStructureVO getTaskStructure(UniversityUserTaskDetUpdateVO dto);


    /**
     * 更新任务完成进度
     * @param dto
     */
    void updateProcess(UniversityTaskSubmitDTO dto);

    /**
     * 项目下任务:更新父级为进行中
     */
    void parentStart(UniversityUserTaskDetUpdateVO dto);

    /**
     * 删除用户任务明细
     * @param userIdList 用户 id
     * @param activityId 课程或项目 id
     * @return
     * <AUTHOR>
     */
    void deleteTaskDet(List<String> userIdList, String activityId);

    void mqEndTask(String objectId);

    /**
     * 课程库待学习任务
     * @param dto
     * @return
     */
    Integer countUnfinishedCourseTask(StudyCenterDTO dto);

    StudyIncompleteVo photofinishingCheck(StudyTaskDTO dto);
}


