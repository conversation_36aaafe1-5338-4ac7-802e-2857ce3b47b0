package com.wxy.system.server.service.university.students.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.CollectionUtils;
import com.wxy.base.util.Common;
import com.wxy.base.util.FeignUtils;
import com.wxy.base.util.StringUtils;
import com.wxy.fastdfs.repository.FdfsRepository;
import com.wxy.member.common.dto.coupon.give.UniversityGiveCouponDTO;
import com.wxy.member.common.dto.coupon.plan.PlanGiveCouponDetDTO;
import com.wxy.member.feign.client.coupon.give.MemberCouponGiveClient;
import com.wxy.report.common.dto.sellingpoints.bounty.ReportSellingPointsBountyUniversityTaskSaveDTO;
import com.wxy.report.feign.client.sellingpoints.bounty.ReportSellingPointsBountyClient;
import com.wxy.system.common.dto.university.distribute.UnlockCourseDto;
import com.wxy.system.common.dto.university.distribute.UnlockProjectDto;
import com.wxy.system.common.dto.university.study.StudyCenterDTO;
import com.wxy.system.common.dto.university.study.StudyIncompleteVo;
import com.wxy.system.common.dto.university.study.StudyTaskDTO;
import com.wxy.system.common.dto.university.study.UniversityTaskSubmitDTO;
import com.wxy.system.common.dto.university.task.ProcessingRecordDTO;
import com.wxy.system.common.dto.university.task.reward.UniversityTaskRewardTypeDetDto;
import com.wxy.system.common.dto.university.task.reward.UniversityTaskRewardTypeDto;
import com.wxy.system.common.vo.study.SysStudyProfileVO;
import com.wxy.system.common.vo.university.course.UniversityStuCourseLecturerVO;
import com.wxy.system.common.vo.university.course.UniversityStuCourseListVO;
import com.wxy.system.common.vo.university.task.UniversityChildTaskVO;
import com.wxy.system.common.vo.university.userdet.UniversityUserTaskDetUpdateVO;
import com.wxy.system.common.vo.university.userdet.UniversityUserTaskVO;
import com.wxy.system.common.vo.university.userdet.UserTaskStructureVO;
import com.wxy.system.server.cvonvert.university.task.TaskDetConvert;
import com.wxy.system.server.dao.university.students.UniversityCourseParticipantTaskDetDao;
import com.wxy.system.server.dao.university.students.UniversityLecturerDao;
import com.wxy.system.server.po.study.profile.SysStudyProfile;
import com.wxy.system.server.po.university.course.UniversityCourse;
import com.wxy.system.server.po.university.project.UniversityProject;
import com.wxy.system.server.po.university.project.UniversityProjectPlan;
import com.wxy.system.server.po.university.record.UniversityHomeworkRecord;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheck;
import com.wxy.system.server.po.university.record.UniversityTestPaperRecord;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTask;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.po.university.task.UniversityTask;
import com.wxy.system.server.po.university.task.UniversityTaskExam;
import com.wxy.system.server.po.university.task.UniversityTaskReward;
import com.wxy.system.server.po.university.task.UniversityTaskRewardType;
import com.wxy.system.server.po.university.task.UniversitySpotReadTask;
import com.wxy.system.server.po.user.SysUser;
import com.wxy.system.server.service.study.profile.ISysStudyProfileService;
import com.wxy.system.server.service.university.course.IUniversityCourseService;
import com.wxy.system.server.service.university.distribute.IUnlockTaskService;
import com.wxy.system.server.service.university.project.IUniversityProjectPlanService;
import com.wxy.system.server.service.university.project.IUniversityProjectService;
import com.wxy.system.server.service.university.record.IUniversityHomeworkRecordService;
import com.wxy.system.server.service.university.record.IUniversityReadOverSpotCheckService;
import com.wxy.system.server.service.university.record.IUniversityTestPaperRecordService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskDetService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskService;
import com.wxy.system.server.service.university.task.IUniversityTaskExamService;
import com.wxy.system.server.service.university.task.IUniversityTaskRewardService;
import com.wxy.system.server.service.university.task.IUniversityTaskRewardTypeService;
import com.wxy.system.server.service.university.task.IUniversityTaskService;
import com.wxy.system.server.service.university.task.IUniversitySpotReadTaskService;
import com.wxy.system.server.service.user.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程学员任务明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
@Service
@Slf4j
public class UniversityUserTaskDetServiceImpl extends ServiceImpl<UniversityCourseParticipantTaskDetDao, UniversityCourseParticipantTaskDet> implements IUniversityUserTaskDetService {

    @Resource
    private IUniversityTaskService taskService;

    @Autowired
    private UniversityLecturerDao lecturerDao;

    @Resource
    private FdfsRepository fdfsRepository;

    @Resource
    private ISysStudyProfileService studyProfileService;

    @Resource
    private TaskDetConvert taskDetConvert;

    @Resource
    private IUniversityTaskRewardService taskRewardService;

    @Resource
    private IUniversityTaskRewardService rewardService;

    @Resource
    private IUniversityTaskRewardTypeService rewardTypeService;

    @Resource
    private MemberCouponGiveClient couponGiveClient;

    @Resource
    private ISysUserService userService;

    @Resource
    private ReportSellingPointsBountyClient sellingPointsBountyClient;

    @Resource
    private IUniversityUserTaskService userTaskService;

    @Resource
    private IUniversityCourseService courseService;

    @Resource
    private IUnlockTaskService unlockTaskService;

    @Resource
    private IUniversityProjectService projectService;

    @Resource
    private IUniversityProjectPlanService projectPlanService;

    @Resource
    private IUniversityTestPaperRecordService paperRecordService;

    @Resource
    private IUniversityHomeworkRecordService homeworkRecordService;

    @Resource
    private IUniversityReadOverSpotCheckService universityReadOverSpotCheckService;

    @Resource
    private IUniversitySpotReadTaskService universitySpotReadTaskService;

    @Resource
    private IUniversityTaskExamService taskExamService;


    /**
    * 新增 课程学员任务明细
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UniversityCourseParticipantTaskDet add(UniversityCourseParticipantTaskDet dto){
        save(dto);
        return dto;
    }

    /**
    * 修改 课程学员任务明细
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UniversityCourseParticipantTaskDet edit(UniversityCourseParticipantTaskDet dto){
        updateById(dto);
        return dto;
    }

    /**
    * 删除 课程学员任务明细
    *
    * @param id 主键
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id){
        baseMapper.deleteById(id);
    }

    /**
    * 根据id获取 课程学员任务明细 详情
    *
    * @param id 主键
    */
    @Override
    public UniversityCourseParticipantTaskDet queryById(String id){
         return checkEntity(id);
    }


    /**
    * 分页查询 课程学员任务明细
    *
    * @param dto 参数
    * @return
    */
    @Override
    public IPage<UniversityCourseParticipantTaskDet>  findPage(IPage<UniversityCourseParticipantTaskDet> page, UniversityCourseParticipantTaskDet dto) {
        return page;
    }

    /**
    * 根据id获取 课程学员任务明细 实体
    *
    * @param id 主键
    */
    @Override
    public UniversityCourseParticipantTaskDet checkEntity(String id){
        if (StringUtils.isBlank(id)){
            throw new AppBizException("999000","课程学员任务明细未查询到", StringUtils.EMPTY);
        }
        UniversityCourseParticipantTaskDet entity = baseMapper.selectById(id);
        if (entity == null){
            throw new AppBizException("999000","课程学员任务明细未查询到", StringUtils.EMPTY);
        }
        return entity;
    }



    /**
     * 根据学员用户id 查询未完成任务
     * @return
     */
    @Override
    public List<UniversityChildTaskVO> findUnfinshTsak(StudyCenterDTO dto) {
        List<UniversityChildTaskVO> taskVOS = this.getBaseMapper().selectUnfinshTsak(dto);

        // 任务奖励
        List<String> taskIds = taskVOS.stream().map(UniversityChildTaskVO::getId).collect(Collectors.toList());
        List<UniversityTaskRewardTypeDto> rewardList = taskRewardService.findRewardByTask(taskIds);
        Map<String, UniversityTaskRewardTypeDto> rewardMap = rewardList.stream().collect(Collectors.toMap(r -> r.getTaskId(), r -> r, (k1, k2) -> k1));

        for (UniversityChildTaskVO taskVO : taskVOS) {
            UniversityTaskRewardTypeDto reward = rewardMap.get(taskVO.getId());
            if (Objects.nonNull(reward)) taskVO.setRewardTypeDtoList(List.of(reward));
        }

        // 设置考试/作业记录信息
        this.setTestPaperAndWork(taskVOS);
        //查询待抽检任务与待批阅任务
        this.setSpotCheckAndExam(taskVOS, Common.getUserId());

        return taskVOS;
    }

    private void setSpotCheckAndExam(List<UniversityChildTaskVO> taskVOS, String userId) {
        // 使用优化后的拆分查询方法
        List<UniversityChildTaskVO> taskVOList = this.getProcessingListOptimized(userId);
        if (CollectionUtils.isEmpty(taskVOList)) {
            return;
        }
        taskVOS.addAll(taskVOList);
    }

    /**
     * 优化后的待处理任务查询方法（查询拆分优化 + 31天日期限制）
     * @param userId 用户ID
     * @return 待处理任务列表
     */
    private List<UniversityChildTaskVO> getProcessingListOptimized(String userId) {
        // 1. 先查询用户的待处理记录ID列表
        List<ProcessingRecordDTO> processingRecords = this.baseMapper.findProcessingRecords(userId);
        if (CollectionUtils.isEmpty(processingRecords)) {
            return new ArrayList<>();
        }

        // 2. 按记录类型分组
        Map<Integer, List<String>> recordsByType = processingRecords.stream()
                .collect(Collectors.groupingBy(
                        ProcessingRecordDTO::getRecordType,
                        Collectors.mapping(ProcessingRecordDTO::getRecordId, Collectors.toList())
                ));

        List<UniversityChildTaskVO> result = new ArrayList<>();

        // 3. 分别查询考试和作业的详细信息
        // 处理批阅记录 (record_type = 1)
        List<String> reviewRecordIds = recordsByType.get(1);
        if (CollectionUtils.isNotEmpty(reviewRecordIds)) {
            // 查询考试批阅记录
            List<UniversityChildTaskVO> examTasks = this.baseMapper.findExamDetailsByRecordIds(reviewRecordIds, userId);
            if (CollectionUtils.isNotEmpty(examTasks)) {
                result.addAll(examTasks);
            }

            // 查询作业批阅记录
            List<UniversityChildTaskVO> homeworkReviewTasks = this.baseMapper.findHomeworkReviewDetailsByRecordIds(reviewRecordIds, userId);
            if (CollectionUtils.isNotEmpty(homeworkReviewTasks)) {
                result.addAll(homeworkReviewTasks);
            }
        }

        // 处理抽检记录 (record_type = 2)
        List<String> spotCheckRecordIds = recordsByType.get(2);
        if (CollectionUtils.isNotEmpty(spotCheckRecordIds)) {
            List<UniversityChildTaskVO> homeworkSpotCheckTasks = this.baseMapper.findHomeworkDetailsByRecordIds(spotCheckRecordIds, userId);
            if (CollectionUtils.isNotEmpty(homeworkSpotCheckTasks)) {
                // 相同 id 的任务只保留一条
                Map<String, UniversityChildTaskVO> taskMap = new LinkedHashMap<>();
                for (UniversityChildTaskVO task : homeworkSpotCheckTasks) {
                    taskMap.putIfAbsent(task.getId(), task);
                }
                result.addAll(taskMap.values());
            }
        }

        return result;
    }


    /**
     * 根据学员用户id 查询课程(不包含项目课程)
     * @param dto
     * @return
     */
    @Override
    public List<UniversityStuCourseListVO> findCourseByUserId(StudyCenterDTO dto) {
        String sysUserId = dto.getSysUserId();
        List<UniversityStuCourseListVO> courseVOS = this.getBaseMapper().selectCourseByUserId(dto);
        if(CollectionUtils.isEmpty(courseVOS)) return courseVOS;

        // 获取培训老师
        String fastDfsUrl = fdfsRepository.getNginxStorageServer() + "/";
        List<String> courseIdList = courseVOS.stream().map(UniversityStuCourseListVO::getCourseId).collect(Collectors.toList());
        List<UniversityStuCourseLecturerVO> lecturerList = lecturerDao.findListDetail(courseIdList, fastDfsUrl);
        Map<String, List<UniversityStuCourseLecturerVO>> lecturerMap = lecturerList.stream().collect(Collectors.groupingBy(UniversityStuCourseLecturerVO::getCourseId));

        // 获取资料详情
        List<String> studyProfileIdList = courseVOS.stream().filter(x->StringUtils.isNotBlank(x.getStudyProfileId())).map(UniversityStuCourseListVO::getStudyProfileId).collect(Collectors.toList());
        Map<String, SysStudyProfile> studyProfileMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(studyProfileIdList)){
            List<SysStudyProfile> studyProfileList = studyProfileService.listByIds(studyProfileIdList);
            studyProfileMap = CollectionUtils.isEmpty(studyProfileList)? new HashMap<>() :
                    studyProfileList.stream().collect(Collectors.toMap(SysStudyProfile::getId, Function.identity(), (k1, k2)->k1));
        }

        //奖励
        List<String> pageCourseIdList = CollectionUtils.isEmpty(courseVOS)? new ArrayList<>() :
                                            courseVOS.stream().map(UniversityStuCourseListVO::getCourseId).collect(Collectors.toList());
        List<UniversityTaskReward> rewardList = rewardService.lambdaQuery() .eq(UniversityTaskReward::getIsValid, 1)
                                                                            .in(UniversityTaskReward::getCourseId,pageCourseIdList)
                                                                            .list();
        Map<String,List<UniversityTaskReward>> rewardMap = CollectionUtils.isEmpty(rewardList)? new HashMap<>() :
                                                            rewardList.stream().collect(Collectors.groupingBy(UniversityTaskReward::getCourseId));
        List<String> rewardIdList = CollectionUtils.isEmpty(rewardList)? new ArrayList<>() :
                                                            rewardList.stream().map(UniversityTaskReward::getId).collect(Collectors.toList());
        Map<String, List<UniversityTaskRewardType>> rewardTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rewardIdList)) {
            List<UniversityTaskRewardType> rewardTypeList = rewardTypeService.lambdaQuery().in(UniversityTaskRewardType::getTaskRewardId, rewardIdList)
                    .list();
            rewardTypeMap = rewardTypeList.stream().collect(Collectors.groupingBy(UniversityTaskRewardType::getTaskRewardId));
        }

        for (UniversityStuCourseListVO vo : courseVOS){
            // 老师
            vo.setLecturerVOList(lecturerMap.get(vo.getCourseId()));
            // 资料
            if (StringUtils.isNotBlank(vo.getStudyProfileId()) && studyProfileMap.containsKey(vo.getStudyProfileId())) {
                SysStudyProfileVO studyProfileVO = new SysStudyProfileVO();
                BeanUtils.copyProperties(studyProfileMap.get(vo.getStudyProfileId()), studyProfileVO);
                vo.setStudyProfileVO(studyProfileVO);
            }
            // 奖励
            List<UniversityTaskReward> taskRewardList = rewardMap.get(vo.getCourseId());
            Map<Integer,List<UniversityTaskReward>> rewardMapByType = CollectionUtils.isEmpty(taskRewardList) ? new HashMap<>() :
                    taskRewardList.stream().collect(Collectors.groupingBy(UniversityTaskReward::getObjectType ));
            List<Integer> rewardCNList = new ArrayList<>();
            for (Map.Entry<Integer,List<UniversityTaskReward>> entry : rewardMapByType.entrySet()){
                List<String> taskRewardIdList = entry.getValue().stream().map(UniversityTaskReward::getId).collect(Collectors.toList());
                for (String rewardId : taskRewardIdList){
                    if (rewardTypeMap.containsKey(rewardId)){
                        rewardCNList.add(entry.getKey());
                        break;
                    }
                }
            }
            vo.setRewardCNList(rewardCNList);
        }

        return courseVOS;
    }



    /**
     * 批量查询学员任务信息
     * @param sysUserId
     * @param taskDetIds
     * @param projectId
     * @return
     */
    @Override
    public List<UniversityChildTaskVO> findTsakList(String sysUserId, List<String> taskDetIds, String projectId) {
        List<UniversityChildTaskVO> taskVOS = this.getBaseMapper().selectTsakList(sysUserId, taskDetIds, projectId);

        // 设置考试/作业记录信息
        this.setTestPaperAndWork(taskVOS);

        return taskVOS;
    }



    private void setTestPaperAndWork(List<UniversityChildTaskVO> taskVOS) {
        if(CollectionUtils.isEmpty(taskVOS)) return;


        //考试
        List<String> participantTaskDetIdList = taskVOS.stream().map(UniversityChildTaskVO::getTaskDetId).collect(Collectors.toList());
        List<UniversityTestPaperRecord> paperRecordList = paperRecordService.lambdaQuery()
                .in(UniversityTestPaperRecord::getUserTaskDetId,participantTaskDetIdList)
                .eq(UniversityTestPaperRecord::getIsValid, 1)
                .list();
        Map<String, UniversityTestPaperRecord> maxPaperRecordMap = CollectionUtils.isEmpty(paperRecordList) ? new HashMap<>() :
                paperRecordList.stream()
                        .collect(Collectors.toMap(
                                UniversityTestPaperRecord::getUserTaskDetId,
                                record -> record,
                                BinaryOperator.maxBy(Comparator.comparing(UniversityTestPaperRecord::getCreateTime))
                        ));
        //作业
        List<UniversityHomeworkRecord> homeworkRecordList = homeworkRecordService.lambdaQuery()
                .in(UniversityHomeworkRecord::getUserTaskDetId,participantTaskDetIdList)
                .eq(UniversityHomeworkRecord::getIsValid, 1)
                .list();
        Map<String, UniversityHomeworkRecord> maxHomeworkRecordMap = CollectionUtils.isEmpty(homeworkRecordList) ? new HashMap<>() :
                homeworkRecordList.stream()
                        .collect(Collectors.toMap(
                                UniversityHomeworkRecord::getUserTaskDetId,
                                record -> record,
                                BinaryOperator.maxBy(Comparator.comparing(UniversityHomeworkRecord::getCreateTime))
                        ));

        List<String> recordIdList = new ArrayList<>();
        recordIdList.addAll(maxPaperRecordMap.values().stream().map(UniversityTestPaperRecord::getId).collect(Collectors.toList()));
        recordIdList.addAll(maxHomeworkRecordMap.values().stream().map(UniversityHomeworkRecord::getId).collect(Collectors.toList()));

        //批阅记录
        List<UniversityReadOverSpotCheck> readOverSpotCheckList = CollectionUtils.isEmpty(recordIdList) ? new ArrayList<>() :
                universityReadOverSpotCheckService.lambdaQuery()
                        .in(UniversityReadOverSpotCheck::getRecordId,recordIdList)
                        .eq(UniversityReadOverSpotCheck::getIsValid, 1)
                        .list();
        Map<String,UniversityReadOverSpotCheck> readOverSpotMap = readOverSpotCheckList.stream()
                .collect(Collectors.toMap(UniversityReadOverSpotCheck::getRecordId, Function.identity(), (k1,k2)->k1));

        // 试卷
        List<String> taskIds = taskVOS.stream().map(UniversityChildTaskVO::getId).collect(Collectors.toList());
        Map<String, Integer> examInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taskIds)) {
            List<UniversityTaskExam> taskExams = taskExamService.lambdaQuery().in(UniversityTaskExam::getTaskId, taskIds).list();
            examInfoMap = taskExams.stream().collect(Collectors.toMap(k -> k.getTaskId(), k -> k.getIsUnShowExamResult(), (k1, k2) -> k1));
        }

        for (UniversityChildTaskVO childTaskVO :  taskVOS){
            if (Objects.isNull(childTaskVO.getUserTaskSingleVo())) continue;

            //添加作业和考试
            if (maxPaperRecordMap.containsKey(childTaskVO.getTaskDetId())){
                childTaskVO.getUserTaskSingleVo().setRecordId(maxPaperRecordMap.get(childTaskVO.getTaskDetId()).getId());
                childTaskVO.getUserTaskSingleVo().setTestPaperStatus(maxPaperRecordMap.get(childTaskVO.getTaskDetId()).getTestPaperStatus());
                childTaskVO.getUserTaskSingleVo().setReadOverStatus(maxPaperRecordMap.get(childTaskVO.getTaskDetId()).getReadOverStatus());
            }
            if (maxHomeworkRecordMap.containsKey(childTaskVO.getTaskDetId())){
                childTaskVO.getUserTaskSingleVo().setRecordId(maxHomeworkRecordMap.get(childTaskVO.getTaskDetId()).getId())
                                                 .setReadOverStatus(maxHomeworkRecordMap.get(childTaskVO.getTaskDetId()).getReadOverStatus())
                                                 .setHomeworkStatus(maxHomeworkRecordMap.get(childTaskVO.getTaskDetId()).getHomeworkStatus());
            }
            if (readOverSpotMap.containsKey(childTaskVO.getUserTaskSingleVo().getRecordId())){
                childTaskVO.getUserTaskSingleVo().setReadOverDetailStatus(readOverSpotMap.get(childTaskVO.getUserTaskSingleVo().getRecordId()).getReadOverStatus());
            }
            childTaskVO.setIsUnShowExamResult(examInfoMap.get(childTaskVO.getId()));
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskUpdateProgressAll(UniversityUserTaskDetUpdateVO dto) {
        log.info("taskUpdateProgressAll-dto({})", dto);
        if (Objects.isNull(dto)) return;
        BigDecimal reachProcess = new BigDecimal(100);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());

        // 更新任务: 考试,作业(完成)
        UniversityCourseParticipantTaskDet taskDet = taskDetConvert.toUniversityCourseTaskDet(dto);
        if (dto.getTeachTool() > 4){
            dto.setProgress(reachProcess);
            taskDet.setProgress(reachProcess);
            if (Objects.isNull(taskDet.getStartUserTime())) taskDet.setStartUserTime(nowTime);
            if (Objects.isNull(taskDet.getEndUserTime())) taskDet.setEndUserTime(nowTime);
            this.updateById(taskDet);
        }

        boolean reach = dto.getTaskStatus() == 2 || dto.getTaskStatus() == 4;// 任务是否完成


        // 任务结构: 项目->课程->任务, 项目->任务, 课程->任务
        UserTaskStructureVO structure = this.getTaskStructure(dto);
        List<UniversityUserTaskDetUpdateVO> peerList = structure.getPeerList();
        List<UniversityUserTaskDetUpdateVO> parentList = structure.getParentList();
        UniversityUserTaskVO topVo = structure.getTask();
        UniversityUserTaskDetUpdateVO parentVo = null;
        Boolean projectCourseReach = false;//项目下课程完成
        Boolean projectTaskReach = false;//项目下任务完成
        log.debug("taskUpdateProgressAll-structure({})", structure);


        // 1.更新学习进度: 课程/项目
        if (CollectionUtils.isNotEmpty(peerList)){
            // 1.1计算进度
            BigDecimal parentProcess = this.getParentProcess(peerList);
            if (CollectionUtils.isEmpty(parentList)){
                // 无父级同级: 设置最上级进度
                topVo.setProgress(parentProcess)
                     .setTaskStatus(topVo.getTaskStatus().equals(0) ? 1 : topVo.getTaskStatus());
            } else {
                // 有父级同级: 设置父级进度-> 计算最上级进度
                for (UniversityUserTaskDetUpdateVO p : parentList) {
                    if (p.getId().equals(dto.getParentId())) {
                        if (Objects.isNull(p.getStartUserTime())) p.setStartUserTime(nowTime);
                        p.setProgress(parentProcess).setTaskStatus(p.getTaskStatus().equals(0) ? 1 : p.getTaskStatus());
                        parentVo = p;
                        break;
                    }
                }
                BigDecimal topProcess = this.getParentProcess(parentList);
                topVo.setProgress(topProcess).setTaskStatus(topVo.getTaskStatus().equals(0) ? 1 : topVo.getTaskStatus());
            }
            // 1.2设置状态
            if (reach) {
                projectTaskReach = dto.getTaskType().equals(1) && CollectionUtils.isEmpty(parentList);
                boolean peerReach = peerList.stream().allMatch(p -> p.getTaskStatus().equals(2) || p.getTaskStatus().equals(4));
                boolean ifDelay = peerList.stream().anyMatch(p -> p.getTaskStatus().equals(4));
                if (CollectionUtils.isEmpty(parentList) && peerReach){
                    // 无父级同级 & 同级全部完成: 设置最上级状态
                    topVo.setTaskStatus(ifDelay ? 4 : 2)
                         .setClaimReward(StringUtils.isNotEmpty(topVo.getTaskRewardId()) ? 1:0)
                         .setEndTime(Objects.isNull(topVo.getEndTime()) && topVo.getTaskType().equals(0) ? nowTime:topVo.getEndTime());
                } else if (CollectionUtils.isNotEmpty(parentList) && peerReach) {
                    // 有父级同级 & 同级全部完成: 设置父级状态-> 最上级状态
                    for (UniversityUserTaskDetUpdateVO p : parentList) {
                        if (p.getId().equals(dto.getParentId())) {
                            p.setTaskStatus(ifDelay ? 4 : 2);
                            parentVo.setTaskStatus(p.getTaskStatus())
                                    .setClaimReward(StringUtils.isNotEmpty(parentVo.getTaskRewardId()) ? 1:0)
                                    .setEndUserTime(Objects.isNull(parentVo.getEndUserTime()) ? nowTime:parentVo.getEndUserTime());
                            projectCourseReach = true;
                            break;
                        }
                    }
                    boolean topReach = parentList.stream().allMatch(p -> p.getTaskStatus().equals(2) || p.getTaskStatus().equals(4));
                    boolean topIfDelay = parentList.stream().anyMatch(p -> p.getTaskStatus().equals(4));
                    if (topReach) topVo.setTaskStatus(topIfDelay ? 4 : 2)
                                       .setClaimReward(StringUtils.isNotEmpty(topVo.getTaskRewardId()) ? 1:0);
                }
            }

            // 更新: 任务最上级,父级(课程)
            userTaskService.updateById(new UniversityCourseParticipantTask().setId(topVo.getId())
                                                                            .setProgress(topVo.getProgress())
                                                                            .setClaimReward(topVo.getClaimReward())
                                                                            .setTaskStatus(topVo.getTaskStatus())
                                                                            .setEndTime(topVo.getEndTime()));
            if (Objects.nonNull(parentVo)){
                this.updateById(new UniversityCourseParticipantTaskDet().setId(parentVo.getId())
                                                                        .setProgress(parentVo.getProgress())
                                                                        .setClaimReward(parentVo.getClaimReward())
                                                                        .setTaskStatus(parentVo.getTaskStatus())
                                                                        .setStartUserTime(parentVo.getStartUserTime())
                                                                        .setEndUserTime(parentVo.getEndUserTime()));
            }
        }


        // 更新领取奖励状态
        if (reach) {
            if (StringUtils.isNotEmpty(taskDet.getTaskRewardId())) {
                taskDet.setClaimReward(1);
                this.updateById(taskDet);
            }
        }


        // 2.任务完成处理
        if (reach) {
            // 2.1解锁
            if (dto.getTaskType().equals(0)) {
                log.debug("taskUpdateProgressAll-unlockCourse({},{})", dto.getDetailId(), topVo.getActivityId());
                unlockTaskService.unlockCourse(UnlockCourseDto.builder().courseId(topVo.getActivityId())
                                                                        .taskId(dto.getDetailId())
                                                                        .sysUserId(dto.getSysUserId()).build());
            }

            if (dto.getTaskType().equals(1) && (projectCourseReach || projectTaskReach)){
                // 获取项目计划id
                String objectId = "";
                if (projectCourseReach) objectId = parentVo.getDetailId();
                if (projectTaskReach) objectId = dto.getDetailId();
                UniversityProjectPlan plan = projectPlanService.lambdaQuery().eq(UniversityProjectPlan::getProjectId, topVo.getActivityId())
                                                                             .eq(UniversityProjectPlan::getProjectObjectId, objectId).one();
                log.debug("taskUpdateProgressAll-unlockProject({})", plan);
                if (Objects.nonNull(plan)) {
                    unlockTaskService.unlockProject(UnlockProjectDto.builder().projectId(topVo.getActivityId())
                                                                              .projectPlanId(plan.getId())
                                                                              .sysUserId(dto.getSysUserId()).build());
                }
            }


            // 2.2奖励: 任务
            this.giveReward(dto, null);
            // 2.3奖励: 课程(项目下/课程库)
            log.debug("taskUpdateProgressAll-giveReward-course({},{})", parentVo, topVo);
            if (Objects.nonNull(parentVo) && parentVo.getClaimReward().equals(1)) this.giveReward(null, parentVo);
            if (Objects.nonNull(topVo) && dto.getTaskType().equals(0) && topVo.getClaimReward().equals(1)) {
                UniversityUserTaskDetUpdateVO couserDTO = taskDetConvert.toUniversityUserTaskDetUpdateVO(topVo);
                couserDTO.setDetailId(topVo.getActivityId());
                this.giveReward(null, couserDTO);
            }
        }

    }



    private BigDecimal getParentProcess(List<UniversityUserTaskDetUpdateVO> peerList) {
        BigDecimal molecule = peerList.stream().filter(t -> Objects.nonNull(t.getProgress())).map(t -> t.getProgress()).reduce(BigDecimal.ZERO, BigDecimal::add);
        return molecule.divide(new BigDecimal(peerList.size()), 0, RoundingMode.HALF_UP);
    }



    public UserTaskStructureVO getTaskStructure(UniversityUserTaskDetUpdateVO dto) {
        // 初始化返回结果
        UserTaskStructureVO structureVO = new UserTaskStructureVO();

        // 初始化任务对象
        UniversityUserTaskVO taskVO = new UniversityUserTaskVO();

        // 校验 dto 和 dto 中的任务 ID 是否为空
        if (dto == null || dto.getParticipantTaskId() == null) {
            return structureVO;
        }

        // 查询任务对象
        UniversityCourseParticipantTask participantTask = userTaskService.getById(dto.getParticipantTaskId());
        if (participantTask == null) {
            return structureVO;
        }

        // 复制任务属性
        BeanUtils.copyProperties(participantTask, taskVO);
        structureVO.setTask(taskVO);

        // 查询所有任务详情
        List<UniversityCourseParticipantTaskDet> taskDetList = this.lambdaQuery()
                .eq(UniversityCourseParticipantTaskDet::getParticipantTaskId, participantTask.getId()).list();

        // 校验任务详情列表是否为空
        if (CollectionUtils.isEmpty(taskDetList)) {
            return structureVO;
        }

        // 如果 TeachTool 是 8，即为课程，处理课程下同级任务
        if (dto.getTeachTool() != null && dto.getTeachTool() == 8) {
            // 过滤出同级结构（无父级）
            List<UniversityCourseParticipantTaskDet> peerTaskDetList = taskDetList.stream()
                    .filter(taskDet -> taskDet.getParentId() == null).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(peerTaskDetList)) {
                return structureVO;
            }

            // 转换并设置同级任务列表
            List<UniversityUserTaskDetUpdateVO> peerList = taskDetConvert.toUniversityCourseParticipantTaskDetList(peerTaskDetList);
            structureVO.setPeerList(peerList);
            return structureVO;

        } else if (dto.getTaskType() != null && dto.getTaskType() == 1) {
            if (StringUtils.isEmpty(dto.getParentId())){
                // 查询课程同级（无父级）
                List<UniversityCourseParticipantTaskDet> detList = taskDetList.stream()
                        .filter(taskDet -> taskDet.getParentId() == null).collect(Collectors.toList());

                // 设置父级任务列表
                structureVO.setPeerList(taskDetConvert.toUniversityCourseParticipantTaskDetList(detList));
            }else {
                // 项目下课程内任务处理
                List<UniversityCourseParticipantTaskDet> taskDets = taskDetList.stream()
                        .filter(taskDet -> taskDet.getParentId() != null && taskDet.getParentId().equals(dto.getParentId()))
                        .collect(Collectors.toList());

                // 设置同级任务列表
                structureVO.setPeerList(taskDetConvert.toUniversityCourseParticipantTaskDetList(taskDets));

                // 查询课程同级（无父级）
                List<UniversityCourseParticipantTaskDet> detList = taskDetList.stream()
                        .filter(taskDet -> taskDet.getParentId() == null).collect(Collectors.toList());

                // 设置父级任务列表
                structureVO.setParentList(taskDetConvert.toUniversityCourseParticipantTaskDetList(detList));
            }
            return structureVO;

        } else {
            // 课程类型下的任务处理，直接设置任务详情列表
            structureVO.setPeerList(taskDetConvert.toUniversityCourseParticipantTaskDetList(taskDetList));
        }

        return structureVO;
    }


    /**
     * 发放任务/课程奖励
     * @param taskDTO 与课程选一传参
     * @param courseDTO 与任务选一传参
     */
    @Transactional(rollbackFor = Exception.class)
    public void giveReward(UniversityUserTaskDetUpdateVO taskDTO, UniversityUserTaskDetUpdateVO courseDTO) {
        log.debug("taskUpdateProgressAll-giveReward({},{})", taskDTO, courseDTO);
        boolean ifTask = Objects.nonNull(taskDTO);

        // 需设置奖品
        if (ifTask && StringUtils.isEmpty(taskDTO.getTaskRewardId())) return;
        if (!ifTask && StringUtils.isEmpty(courseDTO.getTaskRewardId())) return;


        // 获取奖励信息
        List<UniversityTaskRewardTypeDto> rewardList = new ArrayList<>();
        if (ifTask){
            rewardList = taskRewardService.findRewardByTask(List.of(taskDTO.getDetailId()));
        } else {
            rewardList = taskRewardService.findRewardByCourse(List.of(courseDTO.getDetailId()));
        }
        List<BigDecimal> bountyList = rewardList.stream().filter(r -> r.getRewardType().equals(1)).map(r -> r.getRewardBounty()).collect(Collectors.toList());
        List<UniversityTaskRewardTypeDetDto> couponDTOList = rewardList.stream().filter(r -> r.getRewardType().equals(2))
                                                                       .map(r -> r.getRewardTypeDetDtoList())
                                                                       .flatMap(List::stream).collect(Collectors.toList());


        String sysUserId = ifTask ? taskDTO.getSysUserId() : courseDTO.getSysUserId();
        String objectId = ifTask ? taskDTO.getDetailId() : courseDTO.getDetailId();
        if (StringUtils.isEmpty(sysUserId)) return;



        // 发奖
        // 1.考拉币
        if (CollectionUtils.isNotEmpty(bountyList)) {
            String taskName = "";
            if (ifTask){
                UniversityTask task = taskService.lambdaQuery().eq(UniversityTask::getId, objectId).one();
                if (Objects.nonNull(task)) taskName = task.getTaskName();
            } else {
                UniversityCourse course = courseService.lambdaQuery().eq(UniversityCourse::getId, objectId).one();
                if (Objects.nonNull(course)) taskName = course.getCourseTitle();
            }

            String finalTaskName = taskName;
            List<ReportSellingPointsBountyUniversityTaskSaveDTO> bountyDTO = bountyList.stream()
                    .map(b -> new ReportSellingPointsBountyUniversityTaskSaveDTO().setSysUserId(sysUserId)
                                                                                  .setRewardAmount(b)
                                                                                  .setRewardUniversityTaskId(objectId)
                                                                                  .setRewardUniversityTaskName(finalTaskName)
                    ).collect(Collectors.toList());
            log.debug("taskUpdateProgressAll-giveReward-Bounty({})", bountyDTO);
            FeignUtils.processFeignResult(sellingPointsBountyClient.addReportSellingPointsBountyUniversityTaskDet(bountyDTO));
        }


        // 2.优惠券
        if (CollectionUtils.isNotEmpty(couponDTOList)) {
            SysUser user = userService.lambdaQuery().eq(SysUser::getId, sysUserId).one();
            if (Objects.nonNull(user) && StringUtils.isNotEmpty(user.getPhoneNumber())) {
                List<PlanGiveCouponDetDTO> couponDets = couponDTOList.stream()
                                                                     .map(c -> new PlanGiveCouponDetDTO().setMbCouponId(c.getCouponId())
                                                                                                         .setGiveTotal(c.getCouponNum()))
                                                                     .collect(Collectors.toList());
                UniversityGiveCouponDTO giveCDTO = new UniversityGiveCouponDTO().setPhoneNumber(user.getPhoneNumber())
                                                                                .setMbCouponInfoList(couponDets);
                couponGiveClient.universityTaskGive(giveCDTO);
            }
        }

    }



    /**
     * 更新任务完成进度
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcess(UniversityTaskSubmitDTO dto) {
        log.debug("UserTaskDet-updateProcess({})", dto);
        BigDecimal completeValue = new BigDecimal(100);
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        Integer videoTime = dto.getVideoTime();
        BigDecimal dividend = new BigDecimal(videoTime).multiply(completeValue);

        UniversityTask task = taskService.lambdaQuery().eq(UniversityTask::getId, dto.getTaskId()).one();
        Integer teachTool = task.getTeachTool();


        // 未完成可更新进度
        UniversityCourseParticipantTaskDet record = this.lambdaQuery().eq(UniversityCourseParticipantTaskDet::getId, dto.getCourseParticipantTaskDetId()).one();
        if (record.getTaskStatus().equals(2) || record.getTaskStatus().equals(4)) return;


        // 设置进度和状态
        switch (teachTool){
            case 1:// 图片
                // 观看时长校验
                record.setTaskStatus(videoTime >= task.getDuration() ? 2 : 1);
                record.setProgress(
                        record.getTaskStatus().equals(2) ? completeValue : dividend.divide(new BigDecimal(task.getDuration()), 2, RoundingMode.DOWN)
                );
                break;

            case 2:// 视频
                if (task.getCompleteCondition().equals(1)) {
                    // 观看时长校验
                    record.setTaskStatus(videoTime >= task.getDuration() ? 2 : 1);
                    record.setProgress(
                            record.getTaskStatus().equals(2) ? completeValue : dividend.divide(new BigDecimal(task.getDuration()), 2, RoundingMode.DOWN)
                    );
                } else if (task.getCompleteCondition().equals(2)){
                    // 观看到最后
                    record.setTaskStatus(dto.getFinshStudy().equals(1) ? 2 : 1);
                    record.setProgress(dto.getFinshStudy().equals(1) ? completeValue : BigDecimal.ZERO);
                }
                break;

            case 3:// 音频
                if (task.getCompleteCondition().equals(1)) {
                    // 观看时长校验
                    record.setTaskStatus(videoTime >= task.getDuration() ? 2 : 1);
                    record.setProgress(
                            record.getTaskStatus().equals(2) ? completeValue : dividend.divide(new BigDecimal(task.getDuration()), 2, RoundingMode.DOWN)
                    );
                } else if (task.getCompleteCondition().equals(2)){
                    // 观看到最后
                    record.setTaskStatus(dto.getFinshStudy().equals(1) ? 2 : 1);
                    record.setProgress(dto.getFinshStudy().equals(1) ? completeValue : BigDecimal.ZERO);
                }
                break;

            case 4:// 文档
                if (task.getCompleteCondition().equals(1)) {
                    // 观看时长校验
                    record.setTaskStatus(videoTime >= task.getDuration() ? 2 : 1);
                    record.setProgress(
                            record.getTaskStatus().equals(2) ? completeValue : dividend.divide(new BigDecimal(task.getDuration()), 2, RoundingMode.DOWN)
                    );
                } else {
                    // 下载即完成
                    record.setTaskStatus(2);
                    record.setProgress(completeValue);
                }
                break;
            default:
                break;
        }

        // 延期完成处理
        if (record.getTaskStatus().equals(2) && Objects.nonNull(record.getEndTime()) && nowTime.after(record.getEndTime())){
            record.setTaskStatus(4);
        }
        record.setVideoTime(dto.getVideoTime());

        // 更新用户任务结束时间
        if (record.getTaskStatus().equals(2) || record.getTaskStatus().equals(4)) record.setEndUserTime(nowTime);

        // 更新进度,时长信息
        this.updateById(record);


        // 调整 项目/课程进度
        log.debug("UserTaskDet-updateProcess({})", record);
        this.taskUpdateProgressAll(taskDetConvert.toUniversityCourseParticipantTaskDet(record));
//        if (record.getTaskStatus().equals(2) || record.getTaskStatus().equals(4)) {
//        }


    }

    @Override
    public void parentStart(UniversityUserTaskDetUpdateVO dto) {
        //查看是否为项目内课程任务,是则修改课程用户开始时间
        if (StringUtils.isNotEmpty(dto.getParentId())) {
            UniversityCourseParticipantTaskDet courseTaskDet = this.lambdaQuery().eq(UniversityCourseParticipantTaskDet::getId, dto.getParentId())
                    .last(" limit 1").one();
            if (Objects.isNull(courseTaskDet.getStartUserTime())) {
                //设置课程用户开始时间
                courseTaskDet.setStartUserTime(new Timestamp(System.currentTimeMillis()));
                this.updateById(courseTaskDet);
            }
        }
        //1.查询父级有 top 是否为待开始
        log.debug("UserTaskDet-parentStart({})", dto);
        UniversityCourseParticipantTask participantTask = userTaskService.lambdaQuery()
                .eq(UniversityCourseParticipantTask::getId, dto.getParticipantTaskId()).last(" limit 1").one();
        if (dto.getTaskStatus() == 1) {
            //top 未开始，则修改为开始
            if (participantTask.getTaskStatus() == 0) {
                UniversityCourseParticipantTask courseParticipantTask = new UniversityCourseParticipantTask();
                courseParticipantTask.setId(participantTask.getId()).setTaskStatus(1);
                userTaskService.updateById(courseParticipantTask);
            }
            //2.查询是否有上层
            if (StringUtils.isNotEmpty(dto.getParentId())) {
                UniversityCourseParticipantTaskDet participantTaskDet = this.lambdaQuery().eq(UniversityCourseParticipantTaskDet::getId, dto.getParentId()).last(" limit 1").one();
                if (participantTaskDet.getTaskStatus() == 0) {
                    UniversityCourseParticipantTaskDet taskDet = new UniversityCourseParticipantTaskDet();
                    taskDet.setId(participantTaskDet.getId()).setTaskStatus(1);
                    this.updateById(taskDet);
                }
            }
        }
    }

    @Override
    public void deleteTaskDet(List<String> userIdList, String activityId) {
        // 查询用户任务记录
        List<UniversityCourseParticipantTask> taskList = userTaskService.lambdaQuery()
                .in(UniversityCourseParticipantTask::getSysUserId, userIdList)
                .eq(UniversityCourseParticipantTask::getActivityId, activityId)
                .list();

        if (CollectionUtils.isEmpty(taskList)) return;

        // 收集要删除的任务详情和任务ID
        List<String> participantTaskIdList = new ArrayList<>();
        List<String> sysUserIdList = new ArrayList<>();

        taskList.forEach(task -> {
            participantTaskIdList.add(task.getId());
            sysUserIdList.add(task.getSysUserId());
        });

        // 删除用户任务详情记录
        this.lambdaUpdate()
                .in(UniversityCourseParticipantTaskDet::getSysUserId, sysUserIdList)
                .in(UniversityCourseParticipantTaskDet::getParticipantTaskId, participantTaskIdList)
                .remove();

        // 删除用户任务记录
        userTaskService.removeByIds(participantTaskIdList);
    }

    @Override
    public void mqEndTask(String objectId) {
        // 根据任务 task
        UniversityCourseParticipantTask task = userTaskService.queryById(objectId);
        if (task == null) {
            log.error("用户任务为空:{}", objectId);
            return;
        }

        // 1. 判断是否到期自动结训
        UniversityProject project = projectService.lambdaQuery()
                .eq(UniversityProject::getId, task.getActivityId())
                .one();
        if (project == null) {
            log.error("未找到对应项目:{}", task.getActivityId());
            return;
        }

        // 2. 判断是否延期可学, 是否包含延期可学的课程任务
        List<UniversityCourseParticipantTaskDet> taskDetList = this.lambdaQuery()
                .eq(UniversityCourseParticipantTaskDet::getParticipantTaskId, objectId)
                .list();
        if (taskDetList == null || taskDetList.isEmpty()) {
            log.error("任务明细为空:{}", objectId);
            return;
        }

        List<UniversityCourseParticipantTaskDet> updateList = new ArrayList<>();

        // 是否包含过期可学
        long count = taskDetList.stream().filter(item -> item.getIsExpireStudy() == 1).count();
        // 是否包含未完成
        long countUncompleted = taskDetList.stream()
                .filter(item -> item.getTaskStatus() == 0 || item.getTaskStatus() == 1 || item.getTaskStatus() == 3)
                .count();

        if (count < 1) {
            // 非过期可学
            UniversityCourseParticipantTask participantTask = new UniversityCourseParticipantTask();
            if (project.getIsExpireOverTraining() == 1) {
                participantTask.setFinish(1);
                for (UniversityCourseParticipantTaskDet taskDet : taskDetList) {
                    UniversityCourseParticipantTaskDet det = new UniversityCourseParticipantTaskDet();
                    det.setId(taskDet.getId()).setFinish(1);
                    updateList.add(det);
                }
            }
            if (countUncompleted > 0) {
                // 设置为未完成
                participantTask.setTaskStatus(3).setId(task.getId());
                userTaskService.updateById(participantTask);
            }
        }

        // 3. 更新任务状态
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
    }

    /**
     * 课程库待学习任务
     * @param dto
     * @return
     */
    @Override
    public Integer countUnfinishedCourseTask(StudyCenterDTO dto) {
        Integer total = this.getBaseMapper().countUnfinishedCourseTask(dto);
        return Objects.isNull(total) ? 0 : total;
    }

    @Override
    public StudyIncompleteVo photofinishingCheck(StudyTaskDTO dto) {
        dto.setSysUserId(Common.getUserId());
        StudyIncompleteVo vo = new StudyIncompleteVo();
        List<UniversityChildTaskVO> allTasks = new ArrayList<>();

        // 1. 查询待完成作业（全部）
        StudyCenterDTO centerDTO = new StudyCenterDTO();
        centerDTO.setSysUserId(dto.getSysUserId());
        centerDTO.setNameKeyword(dto.getNameKeyword());
        List<UniversityChildTaskVO> unfinishedTasks = this.getBaseMapper().selectUnfinshTsak(centerDTO);

        // 2. 查询用户的待批阅任务ID列表
        List<String> pendingReadTaskIds = this.getBaseMapper().findPendingReadTaskIds(dto.getSysUserId());
        vo.setReadIdList(pendingReadTaskIds);

        if (CollectionUtils.isEmpty(pendingReadTaskIds)) {
            // 如果没有待批阅任务，只返回待完成作业和抽检任务
            allTasks.addAll(unfinishedTasks);
            // 添加抽检任务
            List<UniversityChildTaskVO> spotCheckTasks = this.getBaseMapper().findSpotCheckTasks(dto.getSysUserId());
            allTasks.addAll(spotCheckTasks);
            vo.setUnfinishedTaskList(allTasks);
            return vo;
        }

        // 3. 根据readId确定当前查询阶段
        String currentReadId = dto.getReadId();
        int currentIndex = pendingReadTaskIds.indexOf(currentReadId);

        if (currentIndex == -1) {
            // readId不存在，返回第一阶段数据
            currentIndex = 0;
            currentReadId = pendingReadTaskIds.get(0);
        }

        // 4. 第一次请求：待完成作业 + 第一个批阅任务明细
        if (currentIndex == 0) {
            allTasks.addAll(unfinishedTasks);
            // 添加第一个批阅任务的明细
            List<UniversityChildTaskVO> readDetails = getReadDetailsByTaskId(currentReadId);
            allTasks.addAll(readDetails);
        }
        // 5. 最后一次请求：最后一个批阅任务明细 + 抽检任务
        else if (currentIndex == pendingReadTaskIds.size() - 1) {
            // 添加最后一个批阅任务的明细
            List<UniversityChildTaskVO> readDetails = getReadDetailsByTaskId(currentReadId);
            allTasks.addAll(readDetails);
            // 添加抽检任务
            List<UniversityChildTaskVO> spotCheckTasks = this.getBaseMapper().findSpotCheckTasks(dto.getSysUserId());
            allTasks.addAll(spotCheckTasks);
        }
        // 6. 中间请求：只返回对应批阅任务的明细
        else {
            List<UniversityChildTaskVO> readDetails = getReadDetailsByTaskId(currentReadId);
            allTasks.addAll(readDetails);
        }

        vo.setUnfinishedTaskList(allTasks);
        return vo;
    }

    /**
     * 根据任务ID获取批阅明细
     * @param taskId 任务ID
     * @return 批阅明细列表
     */
    private List<UniversityChildTaskVO> getReadDetailsByTaskId(String taskId) {
        // 查询批阅待办表确定任务类型
        UniversitySpotReadTask spotReadTask = universitySpotReadTaskService.lambdaQuery()
                .eq(UniversitySpotReadTask::getTaskId, taskId)
                .eq(UniversitySpotReadTask::getStatus, 0)
                .eq(UniversitySpotReadTask::getIsValid, 1)
                .one();

        if (spotReadTask == null) {
            return new ArrayList<>();
        }

        // 根据type字段判断是作业还是考试
        if (spotReadTask.getType() == 0) {
            // 作业
            return this.getBaseMapper().findHomeworkReadDetails(taskId);
        } else {
            // 考试
            return this.getBaseMapper().findExamReadDetails(taskId);
        }
    }


}
