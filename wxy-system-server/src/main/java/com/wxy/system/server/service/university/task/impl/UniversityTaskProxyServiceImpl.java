package com.wxy.system.server.service.university.task.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wxy.base.constant.UserConstant;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.CollectionUtils;
import com.wxy.base.util.Common;
import com.wxy.base.util.FeignUtils;
import com.wxy.base.util.StringUtils;
import com.wxy.shop.common.vo.base.ShopBaseInfoVO;
import com.wxy.shop.feign.client.base.ShopBaseClient;
import com.wxy.redis.annotation.RedisLock;
import com.wxy.system.common.dto.university.record.spotcheck.UniversitySpotCheckFindDTO;
import com.wxy.system.common.dto.university.task.UniversityTaskCheckDTO;
import com.wxy.system.common.dto.user.QueryUserListDTO;
import com.wxy.system.common.vo.dept.SimpleDepartmentVO;
import com.wxy.system.common.vo.university.record.eaxm.UniversityTestPaperRecordVo;
import com.wxy.system.common.vo.university.record.homework.UniversityHomeworkRecordVo;
import com.wxy.system.common.vo.university.task.UniversityDetailsVo;
import com.wxy.system.common.vo.university.task.UniversityTaskHomeworkVo;
import com.wxy.system.common.vo.university.task.UniversityTaskUserRelObjectVo;
import com.wxy.system.common.vo.university.task.UniversityTaskVO;
import com.wxy.system.common.vo.user.QueryUserListVO;
import com.wxy.system.server.dao.university.record.UniversityHomeworkRecordDao;
import com.wxy.system.server.dao.university.record.UniversityReadOverSpotCheckUserDao;
import com.wxy.system.server.dao.university.record.UniversityTestPaperRecordDao;
import com.wxy.system.server.dao.university.students.UniversityCourseParticipantTaskDao;
import com.wxy.system.server.dao.university.students.UniversityCourseStudentsDao;
import com.wxy.system.server.dao.university.task.UniversitySpotCheckTaskDao;
import com.wxy.system.server.dao.university.task.UniversitySpotReadTaskDao;
import com.wxy.system.server.dao.university.task.UniversityTaskExamDao;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTask;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.po.university.task.UniversitySpotCheckTask;
import com.wxy.system.server.po.university.task.UniversitySpotReadTask;
import com.wxy.system.server.po.user.SysUser;
import com.wxy.system.server.service.dept.ISysDepartmentService;
import com.wxy.system.server.service.university.students.IUniversityLecturerService;
import com.wxy.system.server.service.university.task.IUniversityTaskExamService;
import com.wxy.system.server.service.university.task.IUniversityTaskProxyService;
import com.wxy.system.server.service.university.task.IUniversityTaskService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskDetService;
import com.wxy.system.server.service.user.ISysRelUserShopService;
import com.wxy.system.server.service.user.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业大学任务代理服务实现类
 * 用于统一处理任务分发逻辑，避免服务间循环依赖
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
public class UniversityTaskProxyServiceImpl implements IUniversityTaskProxyService {

    @Resource
    private IUniversityTaskService universityTaskService;

    @Resource
    private IUniversityUserTaskDetService universityUserTaskDetService;

    @Resource
    private UniversityHomeworkRecordDao universityHomeworkRecordDao;

    @Resource
    private UniversitySpotReadTaskDao universitySpotReadTaskDao;

    @Resource
    private UniversitySpotCheckTaskDao universitySpotCheckTaskDao;

    @Resource
    private IUniversityLecturerService universityLecturerService;

    @Resource
    private UniversityCourseStudentsDao courseStudentsDao;

    @Resource
    private UniversityCourseParticipantTaskDao courseParticipantTaskDao;

    @Resource
    private ISysDepartmentService iSysDepartmentService;

    @Resource
    private ISysUserService iSysUserService;

    @Resource
    private ISysRelUserShopService sysRelUserShopService;

    @Resource
    private UniversityReadOverSpotCheckUserDao universityReadOverSpotCheckUserDao;
    @Resource
    private UniversityTestPaperRecordDao examRecordDao;

    @Resource
    private ShopBaseClient shopBaseClient;

    @Override
    public void distributeReadTasks(UniversityTaskCheckDTO checkDTO, List<String> userIds) {
        log.debug("开始分配批阅老师任务，taskId: {}, userIds: {}", checkDTO.getTaskId(), userIds);

        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表为空，无需分配批阅老师任务");
            return;
        }

        // 为每个老师单独处理删除和新增操作，使用任务ID和用户ID组合加锁
        for (String userId : userIds) {
            if (StringUtils.isNotBlank(userId)) {
                String lockKey = Common.getUserId() + "_" + checkDTO.getTaskId() + "_" + checkDTO.getActivityId() + "_" + checkDTO.getType();
                //是否参与批阅
                Integer isPartake = 0;
                handleReadTaskCompletion(checkDTO, userId, lockKey, Common.getTenantId(), isPartake);
            }
        }

        log.debug("成功分配批阅老师任务，taskId: {}, 分配数量: {}", checkDTO.getTaskId(), userIds.size());
    }

    @Override
    public void distributeSpotCheckTasks(UniversityTaskCheckDTO checkDTO, List<String> userIds) {
        log.debug("开始分配抽检老师任务，taskId: {}, userIds: {}", checkDTO.getTaskId(), userIds);

        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表为空，无需分配抽检老师任务");
            return;
        }

        // 为每个老师单独处理删除和新增操作，使用任务ID和用户ID组合加锁
        for (String userId : userIds) {
            if (StringUtils.isNotBlank(userId)) {
                String lockKey = Common.getUserId() + "_" + checkDTO.getTaskId() + "_" + checkDTO.getActivityId();
                handleSpotCheckTaskDistribution(checkDTO, userId, lockKey, Common.getTenantId());
            }
        }

        log.debug("成功分配抽检老师任务，taskId: {}, 分配数量: {}", checkDTO.getTaskId(), userIds.size());
    }

    @Override
    @RedisLock(param = "lockKey")
    @Transactional(rollbackFor = Exception.class)
    public void handleReadTaskCompletion(UniversityTaskCheckDTO checkDTO, String userId, String lockKey, String tenantId, Integer isPartake) {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
        //使用反射，将gsid设置到request中的的header中去
        reflectSetparam(mockHttpServletRequest, UserConstant.X_TENANT_ID, StringUtils.isNotBlank(tenantId) ? tenantId : "1");

        String taskId = checkDTO.getTaskId();
        String activityId = checkDTO.getActivityId();

        log.debug("开始处理批阅任务完成，taskId: {}, userId: {}, activityId: {}", taskId, userId, activityId);

        // 这里实现原来 UniversitySpotReadTaskServiceImpl.deleteAndAddSpotReadTask 的逻辑
        // 但是不依赖其他循环依赖的服务
        handleSpotReadTaskLogic(checkDTO, userId, isPartake);

        log.debug("完成批阅任务处理，taskId: {}, userId: {}", taskId, userId);
    }

    /**
     * 处理抽检任务分发
     */
    @RedisLock(param = "lockKey")
    @Transactional(rollbackFor = Exception.class)
    public void handleSpotCheckTaskDistribution(UniversityTaskCheckDTO checkDTO, String userId, String lockKey, String tenantId) {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
        reflectSetparam(mockHttpServletRequest, UserConstant.X_TENANT_ID, StringUtils.isNotBlank(tenantId) ? tenantId : "1");

        // 这里实现原来 UniversitySpotCheckTaskServiceImpl.deleteAndAddSpotCheckTask 的逻辑
        // 但是不依赖循环依赖的服务
        handleSpotCheckTaskLogic(checkDTO, userId);
    }

    /**
     * 处理批阅任务的具体逻辑
     */
    private void handleSpotReadTaskLogic(UniversityTaskCheckDTO checkDTO, String userId, Integer isPartake) {
        String taskId = checkDTO.getTaskId();
        log.debug("开始处理单个批阅老师任务，taskId: {}, userId: {}", taskId, userId);

        // 1. 该任务下指定用户的抽检老师记录
        LambdaQueryWrapper<UniversitySpotReadTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UniversitySpotReadTask::getTaskId, taskId)
                .eq(UniversitySpotReadTask::getUserId, userId)
                .eq(UniversitySpotReadTask::getType, checkDTO.getType())
                .eq(UniversitySpotReadTask::getActivityId, checkDTO.getActivityId())
                .eq(UniversitySpotReadTask::getIsValid, 1);

        // 根据actType判断状态
        Integer status = 0; // 默认待处理
        if (ObjectUtil.isNotNull(checkDTO.getActType()) && checkDTO.getActType() == 1 && checkDTO.getType() == 1) {
            // 查询是否有未批阅的考试任务
            UniversitySpotCheckFindDTO dto = new UniversitySpotCheckFindDTO();
            dto.setActivityId(checkDTO.getActivityId());
            dto.setTaskId(taskId);
            dto.setUserId(userId);

            Long unSpotCheckExamCount = universitySpotReadTaskDao.countUnSpotReadExam(dto);
            long totalUnSpotCheckCount = (unSpotCheckExamCount != null ? unSpotCheckExamCount : 0);

            status = totalUnSpotCheckCount > 0 ? 0 : 1; // 0-待处理；1-已处理
            log.debug("用户{}的任务{}未审批数量: 考试={}, 状态={}", userId, taskId, totalUnSpotCheckCount, status);
        } else if (ObjectUtil.isNotNull(checkDTO.getActType()) && checkDTO.getActType() == 1 && checkDTO.getType() == 0) {
            // 查询是否有未批阅的作业任务
            UniversitySpotCheckFindDTO dto = new UniversitySpotCheckFindDTO();
            dto.setActivityId(checkDTO.getActivityId());
            dto.setTaskId(taskId);
            dto.setUserId(userId);

            Long unSpotCheckHomeworkCount = universitySpotReadTaskDao.countUnSpotReadHomework(dto);
            long totalUnSpotCheckCount = (unSpotCheckHomeworkCount != null ? unSpotCheckHomeworkCount : 0);
            status = totalUnSpotCheckCount > 0 ? 0 : 1; // 0-待处理；1-已处理
            log.debug("用户{}的任务{}未审批数量: 作业={}, 状态={}", userId, taskId, totalUnSpotCheckCount, status);
        }

        List<UniversitySpotReadTask> taskList = universitySpotReadTaskDao.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(taskList)) {
            // 不存在则新增
            UniversitySpotReadTask newTask = createSpotReadTask(checkDTO, userId, status, isPartake);
            universitySpotReadTaskDao.insert(newTask);
            log.debug("新增批阅老师记录，taskId: {}, userId: {}, status: {}", taskId, userId, status);
        } else if (taskList.size() == 1) {
            UniversitySpotReadTask existingTask = taskList.get(0);
            UniversitySpotReadTask spotReadTask = new UniversitySpotReadTask();
            spotReadTask.setId(existingTask.getId());
            spotReadTask.setIsPartake(isPartake);
            spotReadTask.setStatus(status);
            universitySpotReadTaskDao.updateById(spotReadTask);
            log.debug("更新批阅老师记录，taskId: {}, userId: {}", taskId, userId);
        } else {
            // 多条记录，删除后新增
            log.warn("存在重复批阅任务记录，执行删除再新增，taskId: {}, userId: {}, activityId: {}",
                    taskId, userId, checkDTO.getActivityId());

            List<String> idsToRemove = taskList.stream()
                    .map(UniversitySpotReadTask::getId)
                    .collect(Collectors.toList());
            universitySpotReadTaskDao.deleteBatchIds(idsToRemove);

            UniversitySpotReadTask newTask = createSpotReadTask(checkDTO, userId, status, isPartake);
            universitySpotReadTaskDao.insert(newTask);
            log.debug("删除重复记录后新增批阅老师记录，taskId: {}, userId: {}, status: {}", taskId, userId, status);
        }

        // 处理批阅完成后的清理逻辑
        if (ObjectUtil.isNotNull(checkDTO.getActType()) && checkDTO.getActType() == 1 ) {
            List<String> userIds = getReadUserIds(checkDTO, userId);
            // 查询出每个userId是否有待完成的任务，需要判断考试或者作业情况的，没有待办的话才加入到deleteUserList中
            List<String> deleteUserList = filterUsersWithoutPendingTasks(checkDTO, userIds);
            log.debug("删除用户id为：{}",deleteUserList);
            if (CollectionUtils.isNotEmpty(deleteUserList)) {
                // 不存在未批阅任务，将未参与批阅的人员剔除
                LambdaQueryWrapper<UniversitySpotReadTask> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(UniversitySpotReadTask::getTaskId, taskId)
                        .eq(UniversitySpotReadTask::getActivityId, checkDTO.getActivityId())
                        .eq(UniversitySpotReadTask::getType, checkDTO.getType())
                        .in(UniversitySpotReadTask::getUserId, deleteUserList)
                        .eq(UniversitySpotReadTask::getIsPartake, 0)
                        .eq(UniversitySpotReadTask::getIsValid, 1);
                List<UniversitySpotReadTask> deleteList = universitySpotReadTaskDao.selectList(deleteWrapper);
                if (CollectionUtils.isNotEmpty(deleteList)) {
                    List<String> ids = deleteList.stream().map(UniversitySpotReadTask::getId).collect(Collectors.toList());
                    universitySpotReadTaskDao.deleteBatchIds(ids);
                    log.debug("清理未参与批阅的人员记录，ids: {}, 清理数量: {}", ids, ids.size());
                }
            }
        }
    }

    /**
     * 处理抽检任务的具体逻辑
     */
    private void handleSpotCheckTaskLogic(UniversityTaskCheckDTO checkDTO, String userId) {
        String taskId = checkDTO.getTaskId();
        String activityId = checkDTO.getActivityId();

        log.debug("开始处理单个抽检老师任务，taskId: {}, userId: {}, activityId: {}", taskId, userId, activityId);

        // 查询该任务下指定用户的抽检老师记录
        LambdaQueryWrapper<UniversitySpotCheckTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UniversitySpotCheckTask::getTaskId, taskId)
                .eq(UniversitySpotCheckTask::getUserId, userId)
                .eq(UniversitySpotCheckTask::getActivityId, activityId)
                .eq(UniversitySpotCheckTask::getIsValid, 1);

        // 根据actType判断状态
        Integer status = 0; // 默认待处理
        if (ObjectUtil.isNotNull(checkDTO.getActType()) && checkDTO.getActType() == 1) {
            // 查询是否有未批阅的任务，如果有的话，则状态为待处理，如果没有，则状态为已处理
            UniversitySpotCheckFindDTO dto = new UniversitySpotCheckFindDTO();
            dto.setActivityId(checkDTO.getActivityId());
            dto.setTaskId(taskId);
            dto.setUserId(userId); // 设置当前用户ID

            // 统计未审批的考试数量
            Long unSpotCheckExamCount = universitySpotCheckTaskDao.countUnSpotCheckExam(dto);
            // 统计未审批的作业数量
            Long unSpotCheckHomeworkCount = universitySpotCheckTaskDao.countUnSpotCheckHomework(dto);

            // 将count相加，如果大于0则说明是待处理，否则为已处理
            long totalUnSpotCheckCount = (unSpotCheckExamCount != null ? unSpotCheckExamCount : 0) +
                                       (unSpotCheckHomeworkCount != null ? unSpotCheckHomeworkCount : 0);

            status = totalUnSpotCheckCount > 0 ? 0 : 1; // 0-待处理；1-已处理
            log.debug("用户{}的任务{}未审批数量: 考试={}, 作业={}, 总计={}, 状态={}",
                    userId, taskId, unSpotCheckExamCount, unSpotCheckHomeworkCount, totalUnSpotCheckCount, status);
        }

        List<UniversitySpotCheckTask> taskList = universitySpotCheckTaskDao.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(taskList)) {
            // 不存在则新增
            UniversitySpotCheckTask newTask = createSpotCheckTask(checkDTO, userId, status);
            universitySpotCheckTaskDao.insert(newTask);
            log.debug("新增抽检老师记录，taskId: {}, userId: {}, status: {}", taskId, userId, status);
        } else if (taskList.size() == 1) {
            // 存在一条记录，更新状态
            UniversitySpotCheckTask existingTask = taskList.get(0);
            UniversitySpotCheckTask updateTask = new UniversitySpotCheckTask();
            updateTask.setId(existingTask.getId());
            updateTask.setStatus(status);
            universitySpotCheckTaskDao.updateById(updateTask);
            log.debug("更新抽检老师记录，taskId: {}, userId: {}, status: {}", taskId, userId, status);
        } else {
            // 多条记录，删除后新增
            log.warn("存在重复抽检任务记录，执行删除再新增，taskId: {}, userId: {}, activityId: {}",
                    taskId, userId, activityId);

            List<String> idsToRemove = taskList.stream()
                    .map(UniversitySpotCheckTask::getId)
                    .collect(Collectors.toList());
            universitySpotCheckTaskDao.deleteBatchIds(idsToRemove);

            UniversitySpotCheckTask newTask = createSpotCheckTask(checkDTO, userId, status);
            universitySpotCheckTaskDao.insert(newTask);
            log.debug("删除重复记录后新增抽检老师记录，taskId: {}, userId: {}, status: {}", taskId, userId, status);
        }
    }

    /**
     * 创建批阅任务
     */
    private UniversitySpotReadTask createSpotReadTask(UniversityTaskCheckDTO checkDTO, String userId, Integer status, Integer isPartake) {
        UniversitySpotReadTask task = new UniversitySpotReadTask();
        task.setTaskId(checkDTO.getTaskId());
        task.setUserId(userId);
        task.setActivityId(checkDTO.getActivityId());
        task.setType(checkDTO.getType());
        task.setStatus(status);
        task.setIsPartake(isPartake);
        task.setIsValid(1);
        task.setTenantId(Common.getTenantId());
        return task;
    }

    /**
     * 创建抽检任务
     */
    private UniversitySpotCheckTask createSpotCheckTask(UniversityTaskCheckDTO checkDTO, String userId, Integer status) {
        UniversitySpotCheckTask spotCheckTask = new UniversitySpotCheckTask();
        spotCheckTask.setTaskId(checkDTO.getTaskId());
        spotCheckTask.setUserId(userId);
        spotCheckTask.setActivityId(checkDTO.getActivityId());
        spotCheckTask.setType(checkDTO.getType());
        spotCheckTask.setStatus(status);

        return spotCheckTask;
    }

    @Override
    public List<UniversitySpotReadTask> findPendingTasks(String userId) {
        LambdaQueryWrapper<UniversitySpotReadTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UniversitySpotReadTask::getUserId, userId)
                .eq(UniversitySpotReadTask::getStatus, 0) // 0-待处理
                .eq(UniversitySpotReadTask::getIsValid, 1);
        return universitySpotReadTaskDao.selectList(queryWrapper);
    }

    @Override
    public UniversityDetailsVo selectDetailsByRecode(String recordId) {
        return universitySpotCheckTaskDao.selectDetailsByRecode(recordId);
    }

    @Override
    public UniversityDetailsVo selectDetailsByExamRecode(String recordId) {
        return universitySpotCheckTaskDao.selectDetailsByExamRecode(recordId);
    }

    @Override
    @RedisLock(param = "lockKey")
    @Transactional(rollbackFor = Exception.class)
    public void deleteAndAddSpotCheckTask(UniversityTaskCheckDTO checkDTO, String userId, String lockKey, String tenantId) {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
        reflectSetparam(mockHttpServletRequest, UserConstant.X_TENANT_ID, StringUtils.isNotBlank(tenantId) ? tenantId : "1");
        reflectSetparam(mockHttpServletRequest, UserConstant.X_USER_ID, StringUtils.isNotBlank(userId) ? userId : "1");

        // 这里实现原来 UniversitySpotCheckTaskServiceImpl.deleteAndAddSpotCheckTask 的逻辑
        handleSpotCheckTaskLogic(checkDTO, userId);
    }

    @Override
    public List<String> getUserIdList(UniversityCourseParticipantTaskDet det, String userId, List<String> userIds, Integer userType, List<String> relList) {
        if(userType.equals(1)){
            UniversityCourseParticipantTask userTask = courseParticipantTaskDao.selectById(det.getParticipantTaskId());
            if (userTask == null){
                throw new AppBizException("999000","课程学员任务未查询到", StringUtils.EMPTY);
            }
            //类型(0:课程;1:项目)
            if(det.getTaskType().equals(0)){
                userIds =  universityLecturerService.findUserIdList(userTask.getActivityId());
            }else{
                userIds = courseStudentsDao.findUserIdList(userTask.getActivityId(), userId,Integer.valueOf(Common.getTenantId()));
            }
        }else {
            if(relList == null || relList.isEmpty()){
                return userIds;
            }
        }
        if(userType.equals(2)){
            //人工评阅 2-指定人员
            userIds = relList;
        }else if(userType.equals(3)){
            //人工评阅 3-指定角色
            userIds = universityReadOverSpotCheckUserDao.findUserByRole(relList,Integer.valueOf(Common.getTenantId()));
        }else if(userType.equals(4)){
            //人工评阅 4-主管
            setManager(relList, userIds, userId);
        }else if(userType.equals(5)){
            //人工评阅 5-指定职务
            userIds = universityReadOverSpotCheckUserDao.findUserByPosition(relList,Integer.valueOf(Common.getTenantId()));
        }else if(userType.equals(6)){
            //人工评阅 6-管辖门店角色
            userIds = setShopRole(relList, userId);
        }else if(userType.equals(7)){
            //人工评阅 7-管辖门店职务
            userIds = setShopPosition(relList, userId);
        }
        return userIds;
    }

    /**
     * 获取批阅用户ID列表
     */
    private List<String> getReadUserIds(UniversityTaskCheckDTO checkDTO, String userId) {
        try {
            //TODO 查询出本次的批阅的用户id来
            UniversityTaskVO task =  universityTaskService.queryById(checkDTO.getTaskId());
            if (task == null ) {
                return new ArrayList<>();
            }
            if ( task.getTaskHomework() == null && task.getTaskExam() == null) {
                return new ArrayList<>();
            }
            List<UniversityTaskUserRelObjectVo> manualReviewList ;
            if (checkDTO.getType() == 0) {
                manualReviewList = task.getTaskHomework().getManualReviewList();
            }else {
                manualReviewList = task.getTaskExam().getManualReviewList();
            }
            if (CollectionUtils.isEmpty(manualReviewList)) {
                return new ArrayList<>();
            }

            // 检查recordId是否存在
            if (StringUtils.isBlank(checkDTO.getRecordId())) {
                return new ArrayList<>();
            }
            String userTaskDetId ;
            if (checkDTO.getType() == 0) {
                //作业情况
                UniversityHomeworkRecordVo recordVo = universityHomeworkRecordDao.getById(checkDTO.getRecordId());
                if (recordVo == null) {
                    return new ArrayList<>();
                }
                userTaskDetId = recordVo.getUserTaskDetId();
            }else {
                //考试情况
                UniversityTestPaperRecordVo recordDaoById = examRecordDao.getById(checkDTO.getRecordId());
                if (recordDaoById == null) {
                    return new ArrayList<>();
                }
                userTaskDetId = recordDaoById.getUserTaskDetId();
            }

            UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(userTaskDetId);
            if (det == null) {
                return new ArrayList<>();
            }

            List<String> userIds = new ArrayList<>();
            for (UniversityTaskUserRelObjectVo userRel : manualReviewList) {
                List<String> userIdList = getUserIdList(det, det.getSysUserId(), userIds, userRel.getRelType(), userRel.getUserRelList());
                if (CollectionUtils.isNotEmpty(userIdList)) {
                    log.debug("根据任务查询批阅老师id：{}",userIdList);
                    userIds.addAll(userIdList);
                }
            }
            return userIds;
        } catch (Exception e) {
            log.error("获取批阅用户ID列表失败，taskId: {}, userId: {}", checkDTO.getTaskId(), userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤出没有待完成任务的用户ID列表
     * @param checkDTO 任务检查DTO
     * @param userIds 用户ID列表
     * @return 没有待完成任务的用户ID列表
     */
    private List<String> filterUsersWithoutPendingTasks(UniversityTaskCheckDTO checkDTO, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        List<String> deleteUserList = new ArrayList<>();

        for (String userId : userIds) {
            if (StringUtils.isBlank(userId)) {
                continue;
            }

            // 构建查询条件
            UniversitySpotCheckFindDTO dto = new UniversitySpotCheckFindDTO();
            dto.setActivityId(checkDTO.getActivityId());
            dto.setTaskId(checkDTO.getTaskId());
            dto.setUserId(userId);

            boolean hasPendingTasks = false;

            // 根据类型判断是考试还是作业
            if (checkDTO.getType() == 1) {
                // 考试情况 - 查询未批阅的考试数量
                Long unSpotCheckExamCount = universitySpotReadTaskDao.countUnSpotReadExam(dto);
                long totalUnSpotCheckCount = (unSpotCheckExamCount != null ? unSpotCheckExamCount : 0);
                hasPendingTasks = totalUnSpotCheckCount > 0;
                log.debug("用户{}的任务{}未批阅考试数量: {}", userId, checkDTO.getTaskId(), totalUnSpotCheckCount);
            } else {
                // 作业情况 - 查询未批阅的作业数量
                Long unSpotCheckHomeworkCount = universitySpotReadTaskDao.countUnSpotReadHomework(dto);
                long totalUnSpotCheckCount = (unSpotCheckHomeworkCount != null ? unSpotCheckHomeworkCount : 0);
                hasPendingTasks = totalUnSpotCheckCount > 0;
                log.debug("用户{}的任务{}未批阅作业数量: {}", userId, checkDTO.getTaskId(), totalUnSpotCheckCount);
            }

            // 如果没有待办任务，加入到删除列表中
            if (!hasPendingTasks) {
                deleteUserList.add(userId);
                log.debug("用户{}没有待办任务，加入删除列表", userId);
            }
        }

        log.debug("过滤后需要删除的用户列表: {}", deleteUserList);
        return deleteUserList;
    }

    /**
     * 设置主管
     */
    private void setManager(List<String> relIdList, List<String> userIds, String userId) {
        for (String levelStr : relIdList) {
            Integer level = 1;
            boolean isLookUp = true;
            boolean isLeader = false;
            if(!levelStr.equals("0")){
                level = Integer.valueOf(levelStr);
            }else{
                isLeader = true;
            }

            //查询用户部门id
            SysUser sysUser = iSysUserService.getById(userId);
            String deptId = sysUser.getSysDepartmentId();

            List<String> list = new ArrayList<>();
            list.add(deptId);
            List<SimpleDepartmentVO> simpleDepartmentVOS = iSysDepartmentService.batchDeptParent(list);

            SimpleDepartmentVO simpleDepartmentVO = simpleDepartmentVOS.get(0);
            String parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level);
            List<String> deptAdmins;
            if (isLookUp){
                deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, true);//过滤离职
                //最多五级
                for(int i=1; i<=5;i++){
                    if(deptAdmins.isEmpty()){
                        parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level+i);
                        deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, true);// 向上找上级要过滤离职人员
                    }else{
                        break;
                    }
                }
            }else {
                deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, false);
            }
            // 自己是领导找上级,后续如果职位是店长也可以考虑继续向上找一级
            if(isLeader) {
                if(deptAdmins.contains(userId)){
                    parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level+1);
                    deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, false);
                }
            }
            if(deptAdmins!= null && !deptAdmins.isEmpty()){
                userIds.addAll(deptAdmins);
            }
        }
    }

    /**
     * 设置管辖门店角色
     */
    private List<String> setShopRole(List<String> relIdList, String userId) {
        String deptId = universityReadOverSpotCheckUserDao.getDept(userId,Integer.parseInt(Common.getTenantId()));
        List<String> userIds;
        QueryUserListDTO queryUserListDTO = new QueryUserListDTO();
        // 根据部门查询门店
        List<ShopBaseInfoVO> shopBaseInfoVOS = FeignUtils.processFeignResult(shopBaseClient.listDeptShop(Collections.singletonList(deptId)));
        List<String> shopIds = shopBaseInfoVOS.stream()
                .map(ShopBaseInfoVO::getId).collect(Collectors.toList());
        queryUserListDTO.setShopIdList(shopIds);
        queryUserListDTO.setRoleIds(relIdList);
        List<QueryUserListVO> userList = sysRelUserShopService.usersByShopList(queryUserListDTO);
        userIds = userList.stream().map(QueryUserListVO::getUserId).distinct().collect(Collectors.toList());
        return userIds;
    }

    /**
     * 设置管辖门店职务
     */
    private List<String> setShopPosition(List<String> relIdList, String userId) {
        String deptId = universityReadOverSpotCheckUserDao.getDept(userId,Integer.parseInt(Common.getTenantId()));
        List<String> userIds;
        QueryUserListDTO queryUserListDTO = new QueryUserListDTO();
        // 根据部门查询门店
        List<ShopBaseInfoVO> shopBaseInfoVOS = FeignUtils.processFeignResult(shopBaseClient.listDeptShop(Collections.singletonList(deptId)));
        List<String> shopIds = shopBaseInfoVOS.stream()
                .map(ShopBaseInfoVO::getId).collect(Collectors.toList());
        queryUserListDTO.setShopIdList(shopIds);
        queryUserListDTO.setPositionIds(relIdList);
        List<QueryUserListVO> userList = sysRelUserShopService.usersByShopList(queryUserListDTO);
        userIds = userList.stream().map(QueryUserListVO::getUserId).distinct().collect(Collectors.toList());
        return userIds;
    }

    /**
     * 获取父部门ID
     */
    public String getParentDeptId(SimpleDepartmentVO simpleDepartmentVO,int currentIndex,int endIndex) {
        if(currentIndex >= endIndex){
            return simpleDepartmentVO.getId();
        }
        SimpleDepartmentVO parentDept = simpleDepartmentVO.getParentDept();
        if(parentDept == null){
            log.error("部门等级超出了范围");
            return StringUtils.EMPTY;
        }
        return this.getParentDeptId(simpleDepartmentVO.getParentDept(),++currentIndex,endIndex);
    }

    /**
     * 使用反射设置请求参数
     */
    private void reflectSetparam(HttpServletRequest request, String key, String value) {
        try {
            Method method = request.getClass().getDeclaredMethod("doAddHeaderValue", String.class, Object.class, boolean.class);
            method.setAccessible(true);
            method.invoke(request, key, value, true);
        } catch (Exception e) {
            log.error("反射设置请求参数失败", e);
        }
    }
}
