package com.wxy.system.server.service.university.record.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wxy.attendance.feign.client.clock.AttnDailyClockResultClient;
import com.wxy.base.constant.UserConstant;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.Common;
import com.wxy.base.util.DateUtils;
import com.wxy.base.util.FeignUtils;
import com.wxy.redis.config.RedisLockAspect;
import com.wxy.shop.common.vo.base.ShopBaseInfoVO;
import com.wxy.shop.feign.client.base.ShopBaseClient;
import com.wxy.system.common.dto.university.task.UniversityTaskCheckDTO;
import com.wxy.system.common.dto.user.QueryUserListDTO;
import com.wxy.system.common.enumerate.SysHttpResult;
import com.wxy.system.common.vo.dept.SimpleDepartmentVO;
import com.wxy.system.common.vo.university.task.UniversityTaskUserRelObjectVo;
import com.wxy.system.common.vo.user.QueryUserListVO;
import com.wxy.system.server.dao.university.record.UniversityReadOverSpotCheckUserDao;
import com.wxy.system.server.dao.university.students.UniversityCourseParticipantTaskDao;
import com.wxy.system.server.dao.university.students.UniversityCourseStudentsDao;
import com.wxy.system.server.po.university.record.UniversityReadOverSpotCheckUser;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTask;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.po.user.SysUser;
import com.wxy.system.server.service.dept.ISysDepartmentService;
import com.wxy.system.server.service.university.record.IUniversityReadOverSpotCheckUserService;
import com.wxy.system.server.service.university.students.IUniversityLecturerService;
import com.wxy.system.server.service.university.task.IUniversityTaskProxyService;
import com.wxy.system.server.service.user.ISysRelUserShopService;
import com.wxy.system.server.service.user.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 企业大学-批阅/抽检人表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 17:06:10
 */

@Slf4j
@Service
public class UniversityReadOverSpotCheckUserServiceImpl extends ServiceImpl<UniversityReadOverSpotCheckUserDao, UniversityReadOverSpotCheckUser> implements IUniversityReadOverSpotCheckUserService   {
    @Resource
    private IUniversityLecturerService universityLecturerService;

    @Resource
    private ShopBaseClient shopBaseClient;

    @Resource
    private IUniversityTaskProxyService universityTaskProxyService;

    @Resource
    private ISysRelUserShopService sysRelUserShopService;



    @Resource
    private ISysDepartmentService iSysDepartmentService;

    @Resource
    private ISysUserService iSysUserService;


//    @Lazy
//    @Autowired(required = false)
//    private IUniversityCourseStudentsService universityCourseStudentsService;
    @Resource
    private UniversityCourseStudentsDao courseStudentsDao;

    @Resource
    private UniversityCourseParticipantTaskDao courseParticipantTaskDao;
    @Autowired
    private RedisLockAspect redisLockAspect;

    @Resource
    private AttnDailyClockResultClient clockResultClient;


    /**
     * 查询
     * @param id
     * @return
     */
    @Override
    public UniversityReadOverSpotCheckUser getById(String id){
        return getBaseMapper().getById(id);
    }


    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(UniversityReadOverSpotCheckUser entity){
        this.getBaseMapper().deleteByIdWithFill(entity);
    }
    
     /**
     * 批量保存
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<UniversityReadOverSpotCheckUser> list){
        return this.saveBatch(list);
    }


    /**
     * 批量更新
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdate(List<UniversityReadOverSpotCheckUser> list){
        return this.updateBatchById(list);
    }

    /**
     * 保存信息
     *
     * @param userRelList
     * @param recordId
     * @param det
     * @param tenantId
     * @param checkUserType
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserIds(List<UniversityTaskUserRelObjectVo> userRelList, Integer recordType, String recordId, UniversityCourseParticipantTaskDet det, String tenantId, String commonUserId, Integer checkUserType)  {

        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));//为空进行新建
        //使用反射，将gsid设置到request中的的header中去
        reflectSetparam(mockHttpServletRequest, UserConstant.X_TENANT_ID, com.wxy.base.util.StringUtils.isNotBlank(tenantId)?tenantId:"1");
        reflectSetparam(mockHttpServletRequest, UserConstant.X_USER_ID,com.wxy.base.util.StringUtils.isNotBlank(commonUserId)?commonUserId:"1" );
        //只判断抽检
        if (StringUtils.isNotBlank(det.getLoopDate()) && 2 == recordType ){
            //查询指定人员日期的休息请假情况,如果休息或请假，则不抽检
            List<String> userIdList =  FeignUtils.processFeignResult(clockResultClient.findByUserDate(det.getLoopDate(), Stream.of(det.getSysUserId()).collect(Collectors.toList())));
            if (userIdList.contains(det.getSysUserId())){
                return;
            }
        }

        try {
            Thread.sleep(10000L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }



        List<UniversityReadOverSpotCheckUser> userIdList = new ArrayList<>();
        //人工评阅(1-讲师;2-指定人员;3-指定角色;4-主管;5-指定职务;6-管辖门店角色;7-管辖门店职务;
        String userId = det.getSysUserId();
        List<String> userIds = new ArrayList<>();


        for (UniversityTaskUserRelObjectVo userRel : userRelList) {
            userIds.addAll(getUserIdList(det, userId, userIds, userRel.getRelType(), userRel.getUserRelList())) ;
        }



        if (userIds.isEmpty()){
            return;
        }
        userIds = this.baseMapper.findUserIds(userIds,Integer.valueOf(Common.getTenantId()));

        userIds = userIds.stream().distinct().collect(Collectors.toList());

        //设置用户
        for (String id : userIds) {
            UniversityReadOverSpotCheckUser user = new UniversityReadOverSpotCheckUser();
            user.setRecordId(recordId).setRecordType(recordType).setSysUserId(id);
            userIdList.add(user);
        }
        UniversityTaskCheckDTO checkDTO = new UniversityTaskCheckDTO();
        String activityId = this.baseMapper.getActivityId(det.getParticipantTaskId());
        checkDTO.setActivityId(activityId).setTaskType(det.getTaskType()).setTaskId(det.getDetailId());
        if (det.getTeachTool() == 5) {
            checkDTO.setType(1);
        }else {
            checkDTO.setType(0);
        }
        if (recordType == 1) {
            //批阅分发待办任务
            universityTaskProxyService.distributeReadTasks(checkDTO,userIds);
        }


        //TODO
        // 1.完成按照任务来进行新增老师代办任务，任务与老师为一对一关系，一个老师和一个任务只会存在一条
        //2.完成代办表的保存，关系表需要是异步的，并且需要加锁
        //3.这个代办表，为通用接口，
        if (recordType == 2 && ObjectUtil.isNotNull(checkUserType) && checkUserType==1) {
            //为抽检才派发老师任务
            universityTaskProxyService.distributeSpotCheckTasks(checkDTO,userIds);
        }
        //批量添加
        this.batchSave(userIdList);
        log.info("分发批阅或者抽检用户权限 recordId ={},type={},userIds={} ",recordId,recordType,userIds);

    }


    public List<String> getUserIdList(UniversityCourseParticipantTaskDet det, String userId, List<String> userIds, Integer userType, List<String> relList) {
        if(userType.equals(1)){
            UniversityCourseParticipantTask userTask = courseParticipantTaskDao.selectById(det.getParticipantTaskId());
            if (userTask == null){
                throw new AppBizException("999000","课程学员任务未查询到", com.wxy.base.util.StringUtils.EMPTY);
            }
            //类型(0:课程;1:项目)
            if(det.getTaskType().equals(0)){
                userIds =  universityLecturerService.findUserIdList(userTask.getActivityId());
            }else{
                userIds = courseStudentsDao.findUserIdList(userTask.getActivityId(), userId,Integer.valueOf(Common.getTenantId()));
            }
        }else {
            if(relList == null || relList.isEmpty()){
                return userIds;
            }
        }
        if(userType.equals(2)){
            //人工评阅 2-指定人员
            userIds = relList;
        }else if(userType.equals(3)){
            //人工评阅 3-指定角色
            userIds = this.baseMapper.findUserByRole(relList,Integer.valueOf(Common.getTenantId()));
        }else if(userType.equals(4)){
            //人工评阅 4-主管
            setManager(relList, userIds, userId);
        }else if(userType.equals(5)){
            //人工评阅 5-指定职务
            userIds = this.baseMapper.findUserByPosition(relList,Integer.valueOf(Common.getTenantId()));
        }else if(userType.equals(6)){
            //人工评阅 6-管辖门店角色
            userIds = setShopRole(relList, userId);
        }else if(userType.equals(7)){
            //人工评阅 7-管辖门店职务
            userIds = setShopPosition(relList, userId);
        }
        return userIds;
    }

    @Override
    public List<String> getUserIds(List<UniversityTaskUserRelObjectVo> userRelList, Integer recordType, String recordId, UniversityCourseParticipantTaskDet det, Integer isSave) {
        List<UniversityReadOverSpotCheckUser> userIdList = new ArrayList<>();
        //人工评阅(1-讲师;2-指定人员;3-指定角色;4-主管;5-指定职务;6-管辖门店角色;7-管辖门店职务;


        String userId = det.getSysUserId();
        List<String> userIds = new ArrayList<>();


        for (UniversityTaskUserRelObjectVo userRel : userRelList) {
            userIds.addAll(getUserIdList(det, userId, userIds, userRel.getRelType(), userRel.getUserRelList())) ;
        }

        if (userIds.isEmpty()){
            return userIds;
        }
        userIds = this.baseMapper.findUserIds(userIds,Integer.valueOf(Common.getTenantId()));
        userIds = userIds.stream().distinct().collect(Collectors.toList());

        //设置用户
        for (String id : userIds) {
            UniversityReadOverSpotCheckUser user = new UniversityReadOverSpotCheckUser();
            user.setRecordId(recordId).setRecordType(recordType).setSysUserId(id);
            userIdList.add(user);
        }

        log.info("分发批阅或者抽检用户权限 recordId ={},type={},userIds={} ",recordId,recordType,userIds);
        if(isSave!= null && isSave.equals(1)){
            //批量添加
            this.batchSave(userIdList);
        }

        log.info("分发批阅或者抽检用户权限 recordId ={},type={},userIds={} ",recordId,recordType,userIds);
        return userIds;
    }

    /**
     * 设置主管
     *
     * @param relIdList
     * @param userIds
     * @param userId
     */
    private void setManager(List<String> relIdList, List<String> userIds, String userId) {
        for (String levelStr : relIdList) {
            Integer level = 1;
            boolean isLookUp = true;
            boolean isLeader = false;
            if(!levelStr.equals("0")){
                level = Integer.valueOf(levelStr);
            }else{
                isLeader = true;
            }

            //查询用户部门id
            SysUser sysUser = iSysUserService.getById(userId);
            String deptId = sysUser.getSysDepartmentId();

            List<String> list = new ArrayList<>();
            list.add(deptId);
            List<SimpleDepartmentVO> simpleDepartmentVOS = iSysDepartmentService.batchDeptParent(list);

            SimpleDepartmentVO simpleDepartmentVO = simpleDepartmentVOS.get(0);
            String parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level);
            List<String> deptAdmins;
            if (isLookUp){
                deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, true);//过滤离职
                //最多五级
                for(int i=1; i<=5;i++){
                    if(deptAdmins.isEmpty()){
                        parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level+i);
                        deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, true);// 向上找上级要过滤离职人员
                    }else{
                        break;
                    }
                }
            }else {
                deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, false);
            }
            // 自己是领导找上级,后续如果职位是店长也可以考虑继续向上找一级
            if(isLeader) {
                if(deptAdmins.contains(userId)){
                    parentDeptId = this.getParentDeptId(simpleDepartmentVO, 1, level+1);
                    deptAdmins = iSysDepartmentService.deptAdmin(parentDeptId, false);
                }
            }
            if(deptAdmins!= null && !deptAdmins.isEmpty()){
                userIds.addAll(deptAdmins);
            }
        }
    }

    /**
     * 设置管辖门店角色
     *
     * @param relIdList
     * @param userId
     * @return
     */

    private List<String> setShopRole(List<String> relIdList, String userId) {

        String deptId = this.baseMapper.getDept(userId,Integer.parseInt(Common.getTenantId()));

        List<String> userIds;
        QueryUserListDTO queryUserListDTO = new QueryUserListDTO();
        // 根据部门查询门店
        List<ShopBaseInfoVO> shopBaseInfoVOS = FeignUtils.processFeignResult(shopBaseClient.listDeptShop(Collections.singletonList(deptId)));
        List<String> shopIds = shopBaseInfoVOS.stream()
                .map(ShopBaseInfoVO::getId).collect(Collectors.toList());
        queryUserListDTO.setShopIdList(shopIds);
        queryUserListDTO.setRoleIds(relIdList);
        List<QueryUserListVO> userList = sysRelUserShopService.usersByShopList(queryUserListDTO);
        userIds = userList.stream().map(QueryUserListVO::getUserId).distinct().collect(Collectors.toList());
        return userIds;
    }

    /**
     * 设置管辖门店职务
     *
     * @param relIdList
     * @param userId
     * @return
     */
    private List<String> setShopPosition(List<String> relIdList, String userId) {
        String deptId = this.baseMapper.getDept(userId,Integer.parseInt(Common.getTenantId()));
        List<String> userIds;
        QueryUserListDTO queryUserListDTO = new QueryUserListDTO();
        // 根据部门查询门店
        List<ShopBaseInfoVO> shopBaseInfoVOS = FeignUtils.processFeignResult(shopBaseClient.listDeptShop(Collections.singletonList(deptId)));
        List<String> shopIds = shopBaseInfoVOS.stream()
                .map(ShopBaseInfoVO::getId).collect(Collectors.toList());
        queryUserListDTO.setShopIdList(shopIds);
        queryUserListDTO.setPositionIds(relIdList);
        List<QueryUserListVO> userList = sysRelUserShopService.usersByShopList(queryUserListDTO);
        userIds = userList.stream().map(QueryUserListVO::getUserId).distinct().collect(Collectors.toList());
        return userIds;
    }
    public String getParentDeptId(SimpleDepartmentVO simpleDepartmentVO,int currentIndex,int endIndex) throws AppBizException {
        if(currentIndex >= endIndex){
            return simpleDepartmentVO.getId();
        }
        SimpleDepartmentVO parentDept = simpleDepartmentVO.getParentDept();
        if(parentDept == null){
            // 部门等级超出了范围 获取最近部门id
            //SysHttpResult.ERROR_DEPT_LEAVE_OUT.appBizException();
            log.error( "++++++++++++++++"+SysHttpResult.ERROR_DEPT_LEAVE_OUT.getMessage());
            return StringUtils.EMPTY;
            //return simpleDepartmentVO.getId();
        }
        return this.getParentDeptId(simpleDepartmentVO.getParentDept(),++currentIndex,endIndex);
    }



    private void reflectSetparam(MockHttpServletRequest request, String key, String value) {
        Class<? extends HttpServletRequest> requestClass = Common.getRequest().getClass();
        System.out.println("request实现类="+requestClass.getName());
        try {
            Method method = request.getClass().getDeclaredMethod("doAddHeaderValue", new Class[]{String.class, Object.class,boolean.class});
            method.setAccessible(true);
            method.invoke(request,key,value,true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
    
    
  
