package com.wxy.system.server.dao.university.students;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wxy.mybatis.plus.dao.MyBaseMapper;
import com.wxy.system.common.dto.university.study.StudyCenterDTO;
import com.wxy.system.common.dto.university.task.ProcessingRecordDTO;
import com.wxy.system.common.vo.university.course.UniversityStuCourseListVO;
import com.wxy.system.common.vo.university.task.UniversityChildTaskVO;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 课程学员任务明细 mapper
*
* <AUTHOR>
* @since 2024-09-13
*/
public interface UniversityCourseParticipantTaskDetDao extends MyBaseMapper<UniversityCourseParticipantTaskDet> {

    /**
    * 分页查询课程学员任务明细
    *
    * @param page
    * @param dto
    * @return
    */
    IPage<UniversityCourseParticipantTaskDet> findPage(IPage<UniversityCourseParticipantTaskDet> page, @Param("dto") UniversityCourseParticipantTaskDet dto);


    List<UniversityChildTaskVO> selectUnfinshTsak(@Param("dto") StudyCenterDTO dto);

    List<UniversityStuCourseListVO> selectCourseByUserId(@Param("dto")StudyCenterDTO dto);

    List<UniversityChildTaskVO> selectTsakList(@Param("sysUserId") String sysUserId, @Param("taskDetIds") List<String> taskDetIds,
                                               @Param("projectId")String projectId);

    /**
     * 课程库待学习任务: 为学习,学习中
     * @param dto
     * @return
     */
    Integer countUnfinishedCourseTask(@Param("dto") StudyCenterDTO dto);

    /**
     * 查询待批阅的考试记录
     * @param userId 用户ID
     * @return 考试记录列表
     */
    List<UniversityChildTaskVO> findExamToReview(@Param("userId") String userId);

    /**
     * 查询待批阅的作业记录
     * @param userId 用户ID
     * @return 作业记录列表
     */
    List<UniversityChildTaskVO> findHomeworkToReview(@Param("userId") String userId);

    /**
     * 查询待抽检的记录
     * @param userId 用户ID
     * @return 抽检记录列表
     */
    List<UniversityChildTaskVO> findTasksToSpotCheck(@Param("userId") String userId);

    /**
     * 查询所有待处理任务（待批阅和待抽检）
     * @param userId 用户ID
     * @return 待处理任务列表
     */
    List<UniversityChildTaskVO> processingList(@Param("userId") String userId);

    /**
     * 查询用户的待处理记录ID列表（近31天）
     * @param userId 用户ID
     * @return 记录ID和类型列表
     */
    List<ProcessingRecordDTO> findProcessingRecords(@Param("userId") String userId);

    /**
     * 根据记录ID批量查询考试详细信息（近31天）
     * @param recordIds 记录ID列表
     * @param userId 用户ID
     * @return 考试任务详细信息
     */
    List<UniversityChildTaskVO> findExamDetailsByRecordIds(@Param("recordIds") List<String> recordIds, @Param("userId") String userId);

    /**
     * 根据记录ID批量查询作业详细信息（近31天） - 抽检逻辑
     * @param recordIds 记录ID列表
     * @param userId 用户ID
     * @return 作业任务详细信息
     */
    List<UniversityChildTaskVO> findHomeworkDetailsByRecordIds(@Param("recordIds") List<String> recordIds, @Param("userId") String userId);

    /**
     * 查询用户的待批阅任务ID列表
     * @param userId 用户ID
     * @return 待批阅任务ID列表
     */
    List<String> findPendingReadTaskIds(@Param("userId") String userId);

    /**
     * 根据任务ID查询批阅明细（作业）
     * @param taskId 任务ID
     * @return 批阅明细列表
     */
    List<UniversityChildTaskVO> findHomeworkReadDetails(@Param("taskId") String taskId);

    /**
     * 根据任务ID查询批阅明细（考试）
     * @param taskId 任务ID
     * @return 批阅明细列表
     */
    List<UniversityChildTaskVO> findExamReadDetails(@Param("taskId") String taskId);

    /**
     * 查询用户的待抽检任务（只返回任务名称）
     * @param userId 用户ID
     * @return 抽检任务列表
     */
    List<UniversityChildTaskVO> findSpotCheckTasks(@Param("userId") String userId);

    /**
     * 根据记录ID批量查询作业批阅详细信息（近31天） - 批阅逻辑
     * @param recordIds 记录ID列表
     * @param userId 用户ID
     * @return 作业批阅任务详细信息
     */
    List<UniversityChildTaskVO> findHomeworkReviewDetailsByRecordIds(@Param("recordIds") List<String> recordIds, @Param("userId") String userId);
}