package com.wxy.system.server.service.university.task;

import com.wxy.system.common.dto.university.task.UniversityTaskCheckDTO;
import com.wxy.system.common.vo.university.task.UniversityDetailsVo;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.po.university.task.UniversitySpotReadTask;

import java.util.List;

/**
 * 企业大学任务代理服务接口
 * 用于统一处理任务分发逻辑，避免服务间循环依赖
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IUniversityTaskProxyService {

    /**
     * 分发批阅任务
     *
     * @param checkDTO 任务检查DTO
     * @param userIds  用户ID列表
     */
    void distributeReadTasks(UniversityTaskCheckDTO checkDTO, List<String> userIds);

    /**
     * 分发抽检任务
     *
     * @param checkDTO 任务检查DTO
     * @param userIds  用户ID列表
     */
    void distributeSpotCheckTasks(UniversityTaskCheckDTO checkDTO, List<String> userIds);

    /**
     * 处理批阅完成后的任务分发
     *
     * @param checkDTO  任务检查DTO
     * @param userId    用户ID
     * @param lockKey   锁键
     * @param tenantId  租户ID
     * @param isPartake 是否参与
     */
    void handleReadTaskCompletion(UniversityTaskCheckDTO checkDTO, String userId, String lockKey, String tenantId, Integer isPartake);

    /**
     * 查询当前用户的待办任务列表
     *
     * @param userId 用户ID
     * @return 待办任务列表
     */
    List<UniversitySpotReadTask> findPendingTasks(String userId);

    /**
     * 获取用户ID列表
     *
     * @param det 课程参与者任务详情
     * @param userId 用户ID
     * @param userIds 用户ID列表
     * @param userType 用户类型
     * @param relList 关联列表
     * @return 用户ID列表
     */
    List<String> getUserIdList(UniversityCourseParticipantTaskDet det, String userId, List<String> userIds, Integer userType, List<String> relList);

    /**
     * 根据作业记录ID查询详情信息
     *
     * @param recordId 作业记录ID
     * @return 详情信息
     */
    UniversityDetailsVo selectDetailsByRecode(String recordId);

    /**
     * 根据考试记录ID查询详情信息
     *
     * @param recordId 考试记录ID
     * @return 详情信息
     */
    UniversityDetailsVo selectDetailsByExamRecode(String recordId);

    /**
     * 删除并添加抽检任务
     *
     * @param checkDTO 任务检查DTO
     * @param userId 用户ID
     * @param lockKey 锁键
     * @param tenantId 租户ID
     */
    void deleteAndAddSpotCheckTask(UniversityTaskCheckDTO checkDTO, String userId, String lockKey, String tenantId);
}
