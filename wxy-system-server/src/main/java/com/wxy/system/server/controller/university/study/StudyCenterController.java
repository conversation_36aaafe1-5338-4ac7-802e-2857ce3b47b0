package com.wxy.system.server.controller.university.study;

import com.wxy.base.entity.DataResult;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.CollectionUtils;
import com.wxy.base.util.Common;
import com.wxy.system.common.dto.university.study.StudyCenterDTO;
import com.wxy.system.common.dto.university.study.StudyIncompleteVo;
import com.wxy.system.common.dto.university.study.StudyTaskDTO;
import com.wxy.system.common.enumerate.SysHttpResult;
import com.wxy.system.common.vo.university.course.UniversityStuCourseListVO;
import com.wxy.system.common.vo.university.project.UniversityPersonalProjectDetailVo;
import com.wxy.system.common.vo.university.project.UniversityPersonalProjectVo;
import com.wxy.system.common.vo.university.study.StudyCenterVo;
import com.wxy.system.common.vo.university.task.UniversityChildTaskVO;
import com.wxy.system.server.service.university.course.IUniversityCourseService;
import com.wxy.system.server.service.university.project.IUniversityProjectService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskDetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* <p>
 * APP-学习中心 前端控制器
 * </p>
*
*/
@Api(tags = "学习中心")
@RestController
@Slf4j
@RequestMapping("/study-center")
public class StudyCenterController {

    @Resource
    private IUniversityUserTaskDetService userTaskDetService;

    @Resource
    private IUniversityCourseService courseService;

    @Resource
    private IUniversityProjectService projectService;



    @ApiOperation("学习中心-详情")
    @PostMapping(value = "/query/personal/study-info")
    public DataResult<StudyCenterVo> studyCenterInfo(@Valid @RequestBody StudyCenterDTO dto) throws AppBizException {
        StudyCenterVo vo = new StudyCenterVo();
        dto.setSysUserId(Common.getUserId());

        // 未完成任务
//        List<UniversityChildTaskVO> unfinshTsak = userTaskDetService.findUnfinshTsak(dto);
//        if (CollectionUtils.isNotEmpty(unfinshTsak)) vo.setUnfinishedTaskList(unfinshTsak);
        // 课程列表
        List<UniversityStuCourseListVO> courseList = userTaskDetService.findCourseByUserId(dto);


        if (CollectionUtils.isNotEmpty(courseList)) vo.setCourseList(courseList);
        vo.setNeedStudyTotal(userTaskDetService.countUnfinishedCourseTask(dto));
        return SysHttpResult.SUCCESS.getDataResult(vo);
    }

    @ApiOperation("学习中心-待完成任务")
    @PostMapping(value = "/query/personal/unfinshTsak")
    public DataResult<StudyIncompleteVo> unfinshTsak(@Valid @RequestBody StudyTaskDTO dto) throws AppBizException {

        // 未完成任务与待批阅id集合
        StudyIncompleteVo unfinshTsak = userTaskDetService.photofinishingCheck(dto);
        return SysHttpResult.SUCCESS.getDataResult(unfinshTsak);
    }



    @ApiOperation("个人项目-列表")
    @PostMapping(value = "/findList/personal/project")
    public DataResult<List<UniversityPersonalProjectVo>> findPersonalProject(@Valid @RequestBody StudyCenterDTO dto) throws AppBizException {
        dto.setSysUserId(Common.getUserId());
        return SysHttpResult.SUCCESS.getDataResult(projectService.findPersonalProject(dto));
    }



    @ApiOperation("个人项目-详情")
    @PostMapping(value = "/query/personal/project/detail")
    public DataResult<UniversityPersonalProjectDetailVo> queryPersonalProject(@Valid @RequestBody StudyCenterDTO dto) throws AppBizException {
        dto.setSysUserId(Common.getUserId());
        return SysHttpResult.SUCCESS.getDataResult(projectService.queryPersonalProject(dto));
    }



//    @ApiOperation("任务完成(音频图文类)-提交")
//    @PostMapping(value = "/profile/task/submit")
//    public DataResult submitTask(@Valid @RequestBody UniversityTaskSubmitDTO dto) throws AppBizException {
//        userTaskDetService.updateProcess(dto);
//        return SysHttpResult.SUCCESS.getDataResult();
//    }

}