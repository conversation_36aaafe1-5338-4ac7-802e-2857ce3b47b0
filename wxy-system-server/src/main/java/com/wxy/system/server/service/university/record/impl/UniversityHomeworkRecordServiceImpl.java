package com.wxy.system.server.service.university.record.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wxy.base.exception.AppBizException;
import com.wxy.base.util.CollectionUtils;
import com.wxy.base.util.Common;
import com.wxy.base.util.StringUtils;
import com.wxy.system.common.dto.university.message.UniversityMessageDTO;
import com.wxy.system.common.dto.university.record.UniversityBeginHomeworkDTO;
import com.wxy.system.common.dto.university.record.UniversityHomeworkDescDTO;
import com.wxy.system.common.dto.university.record.UniversityHomeworkRecordFindDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityHomeworkReadOverSaveDTO;
import com.wxy.system.common.dto.university.record.readover.UniversityPcReadOverSaveDTO;
import com.wxy.system.common.enumerate.SysHttpResult;
import com.wxy.system.common.enumerate.university.MessageTypeEnum;
import com.wxy.system.common.vo.exam.SysExamQuestionAnswerDetailVo;
import com.wxy.system.common.vo.exam.SysStudyProfileDetailVo;
import com.wxy.system.common.vo.university.record.UniversityTaskHomeworkDescVo;
import com.wxy.system.common.vo.university.record.homework.*;
import com.wxy.system.common.vo.university.task.*;
import com.wxy.system.common.vo.university.userdet.UniversityUserTaskDetUpdateVO;
import com.wxy.system.server.cvonvert.university.record.HomeworkRecordConvert;
import com.wxy.system.server.dao.university.record.UniversityHomeworkRecordDao;
import com.wxy.system.server.po.university.course.UniversityCourse;
import com.wxy.system.server.po.university.project.UniversityProject;
import com.wxy.system.server.po.university.record.*;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTask;
import com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet;
import com.wxy.system.server.service.exam.ISysExamQuestionAnswerService;
import com.wxy.system.server.service.exam.ISysRelExamQuestionAttachService;
import com.wxy.system.server.service.study.profile.ISysStudyProfileService;
import com.wxy.system.server.service.university.course.IUniversityCourseService;
import com.wxy.system.server.service.university.message.IUniversityMessageRecordService;
import com.wxy.system.server.service.university.project.IUniversityProjectService;
import com.wxy.system.server.service.university.record.*;
import com.wxy.system.server.service.university.students.IUniversityUserTaskDetService;
import com.wxy.system.server.service.university.students.IUniversityUserTaskService;
import com.wxy.system.server.service.university.task.IUniversityTaskHomeworkService;
import com.wxy.system.server.service.university.task.IUniversityTaskService;
import com.wxy.system.server.util.TaskTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 企业大学-作业记录表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 17:06:10
 */

@Slf4j
@Service
public class UniversityHomeworkRecordServiceImpl extends ServiceImpl<UniversityHomeworkRecordDao, UniversityHomeworkRecord> implements IUniversityHomeworkRecordService   {


    @Resource
    private ISysStudyProfileService studyProfileService;

    @Resource
    private IUniversityHomeworkQuestionService universityHomeworkQuestionService;

    @Resource
    private IUniversityHomeworkQuestionFileService universityHomeworkQuestionFileService;

    @Resource
    private IUniversityHomeworkQuestionDetService universityHomeworkQuestionDetService;

    @Resource
    private IUniversityTaskService universityTaskService;

    @Resource
    private IUniversityUserTaskDetService universityUserTaskDetService;
    @Resource
    private IUniversityTaskHomeworkService universityTaskHomeworkService;

    @Resource
    private HomeworkRecordConvert homeworkRecordConvert;


    @Resource
    private ISysRelExamQuestionAttachService sysRelExamQuestionAttachService;

    @Resource
    private ISysExamQuestionAnswerService sysExamQuestionAnswerService;


    @Autowired(required = false)
    @Lazy
    private IUniversityReadOverSpotCheckUserService universityReadOverSpotCheckUserService;


    @Autowired(required = false)
    private IUniversityReadOverSpotCheckService universityReadOverSpotCheckService;

    @Resource
    private IUniversityMessageRecordService messageRecordService;

    @Resource
    private IUniversityUserTaskService userTaskService;

    @Resource
    private IUniversityProjectService projectService;

    @Resource
    private IUniversityCourseService courseService;



    /**
     * 查询
     *
     * @param recordId
     * @return
     */
    @Override
    public UniversityHomeworkRecordAllVo getById(String recordId){

        UniversityHomeworkRecordAllVo vo = new UniversityHomeworkRecordAllVo();
        //获取回答记录
        UniversityHomeworkRecordVo record = this.baseMapper.getById(recordId);

        //设置回答信息
        setRecord(recordId,record);
        //设置答案
        vo.setRecord(record);

        //获取用户任务明细
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());

        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        vo.setTask(task);

        //题目列表
        List<UniversityHomeworkSubmitQuestionVo> questionVoList = setQuestionList(recordId,record);
        vo.setQuestionList(questionVoList);

        return vo;
    }

    /**
     * 题目列表
     *
     * @param recordId
     * @param record
     * @return
     */
    @NotNull
    private List<UniversityHomeworkSubmitQuestionVo> setQuestionList(String recordId, UniversityHomeworkRecordVo record) {
        List<UniversityHomeworkSubmitQuestionVo> questionVoList= universityTaskHomeworkService.findQuestionList(recordId);
        List<String> questionIdList = questionVoList.stream().map(UniversityHomeworkSubmitQuestionVo::getQuestionId).collect(Collectors.toList());
        List<String> recordIdList = questionVoList.stream().map(UniversityHomeworkSubmitQuestionVo::getId).collect(Collectors.toList());

        Map<String, List<SysStudyProfileDetailVo>> studyProfileDetailVoListMap = new HashMap<>();

        Map<String, List<SysStudyProfileDetailVo>> AnalysisStudyProfileIdListMap = new HashMap<>();

        Map<String, List<SysStudyProfileDetailVo>> userSysStudyProfileListMap = new HashMap<>();

        Map<String, List<SysExamQuestionAnswerDetailVo>> answerDetailVosByIdListMap = new HashMap<>();

        Map<String, List<SysExamQuestionAnswerDetailVo>> userSysExamQuestionAnswerDetailVosMap = new HashMap<>();
        //查询题目题干附件
        List<SysStudyProfileDetailVo>  sysStudyProfileDetailVoList = sysRelExamQuestionAttachService.findStudyProfileByIdList(questionIdList,1);
        if (!sysStudyProfileDetailVoList.isEmpty()) {
            studyProfileDetailVoListMap = sysStudyProfileDetailVoList.stream().collect(Collectors.groupingBy(SysStudyProfileDetailVo::getSysExamQuestionId));

        }

        //查询题目解析附件
        List<SysStudyProfileDetailVo>  AnalysisStudyProfileIds = sysRelExamQuestionAttachService.findStudyProfileByIdList(questionIdList,0);
        if (!sysStudyProfileDetailVoList.isEmpty()) {
            AnalysisStudyProfileIdListMap = AnalysisStudyProfileIds.stream().collect(Collectors.groupingBy(SysStudyProfileDetailVo::getSysExamQuestionId));
        }

        //查询题目员工回答附件
        List<SysStudyProfileDetailVo> userSysStudyProfileList = universityTaskHomeworkService.findUserSysStudyProfileList(recordIdList);
        if (!userSysStudyProfileList.isEmpty()) {
            userSysStudyProfileListMap = userSysStudyProfileList.stream().collect(Collectors.groupingBy(SysStudyProfileDetailVo::getHomeworkQuestionId));
        }

        //查询题目选项
        List<SysExamQuestionAnswerDetailVo> answerDetailVosByIdList = sysExamQuestionAnswerService.findAnswerDetailVosByIdList(questionIdList);
        if (!answerDetailVosByIdList.isEmpty()) {
            answerDetailVosByIdListMap = answerDetailVosByIdList.stream().collect(Collectors.groupingBy(SysExamQuestionAnswerDetailVo::getSysExamQuestionId));
        }

        //查询员工回答选项
        List<SysExamQuestionAnswerDetailVo> userSysExamQuestionAnswerDetailVos = universityHomeworkQuestionDetService.findAnswerDetailVosByIdList(recordId);
        if (!userSysExamQuestionAnswerDetailVos.isEmpty()) {
            userSysExamQuestionAnswerDetailVosMap = userSysExamQuestionAnswerDetailVos.stream().collect(Collectors.groupingBy(SysExamQuestionAnswerDetailVo::getHomeworkQuestionId));
        }


        Map<String, UniversityHomeworkQuestionVo> answerMap = new HashMap<>();
        if(record.getAnswerList()!= null && !record.getAnswerList().isEmpty()){
            answerMap = record.getAnswerList().stream().collect(Collectors.toMap(UniversityHomeworkQuestionVo::getQuestionId, t->t,(k1, k2)->k1));
        }


        for (UniversityHomeworkSubmitQuestionVo universityHomeworkQuestionVo : questionVoList){
            universityHomeworkQuestionVo.setQuestionAnalysisSysStudyProfileList(AnalysisStudyProfileIdListMap.get(universityHomeworkQuestionVo.getQuestionId()));
            universityHomeworkQuestionVo.setSysStudyProfileList(studyProfileDetailVoListMap.get(universityHomeworkQuestionVo.getQuestionId()));
            universityHomeworkQuestionVo.setUserSysStudyProfileList(userSysStudyProfileListMap.get(universityHomeworkQuestionVo.getId()));
            universityHomeworkQuestionVo.setSysExamQuestionAnswerDetailVos(answerDetailVosByIdListMap.get(universityHomeworkQuestionVo.getQuestionId()));
            universityHomeworkQuestionVo.setUserSysExamQuestionAnswerDetailVos(userSysExamQuestionAnswerDetailVosMap.get(universityHomeworkQuestionVo.getId()));
            universityHomeworkQuestionVo.setAnswerVo(answerMap.get(universityHomeworkQuestionVo.getQuestionId()));
        }
        return questionVoList;
    }


    /**
     * 设置信息
     *
     * @param recordId
     * @param record
     */
    private void setRecord(String recordId, UniversityHomeworkRecordVo record) {
        //作业记录-考题列表
        List<UniversityHomeworkQuestion> answerList = universityHomeworkQuestionService.findListByReord(recordId);
        List<UniversityHomeworkQuestionVo> answerVoList = homeworkRecordConvert.toUniversityHomeworkQuestionVoList(answerList);

        //作业记录-考题明细列表
        List<UniversityHomeworkQuestionDet> answerDetList = universityHomeworkQuestionDetService.findListByReord(recordId);
        List<UniversityHomeworkQuestionDetVo> answerDetVoList = homeworkRecordConvert.toUniversityHomeworkQuestionDetVoList(answerDetList);
        Map<String, List<UniversityHomeworkQuestionDetVo>> answerDetMap = answerDetVoList.stream()
                .collect(Collectors.groupingBy(UniversityHomeworkQuestionDetVo::getHomeworkQuestionId));

        //作业记录-考题附件
        List<UniversityHomeworkQuestionFile> questionFileList = universityHomeworkQuestionFileService.findListByReord(recordId);
        List<UniversityHomeworkQuestionFileVo> questionFileVoList = homeworkRecordConvert.toUniversityHomeworkQuestionFileVoList(questionFileList);
        List<String> fileList = questionFileVoList.stream().map(UniversityHomeworkQuestionFileVo::getSysVideoMultimediaId).collect(Collectors.toList());
        if(!fileList.isEmpty()){
            //获取附件信息
            List<SysStudyProfileDetailVo> fileDetList = studyProfileService.findDetailByIds(fileList);
            //设置详情到附件关系表里
            Map<String, SysStudyProfileDetailVo> fileDetMap = fileDetList.stream().collect(Collectors.toMap(SysStudyProfileDetailVo::getId, t -> t, (k1, k2) -> k1));
            for (UniversityHomeworkQuestionFileVo fileVo : questionFileVoList) {
                SysStudyProfileDetailVo fileDet = fileDetMap.get(fileVo.getSysVideoMultimediaId());
                if(fileDet!= null){
                    fileVo.setFileDetailVo(fileDet);
                }
            }
        }

        // 设置回答和附件的信息Map
        Map<String, List<UniversityHomeworkQuestionFileVo>> questionFileMap = questionFileVoList.stream().collect(Collectors.groupingBy(t -> t.getHomeworkQuestionId()));

        //设置回答信息
        for (UniversityHomeworkQuestionVo answer : answerVoList) {
            List<UniversityHomeworkQuestionDetVo> detList = answerDetMap.get(answer.getId());
            List<UniversityHomeworkQuestionFileVo> fileVList = questionFileMap.get(answer.getId());
            //设置详细回答
            if(detList!= null && !detList.isEmpty()){
                answer.setAnswerDetList(detList);
            }
            //设置附件
            if(fileVList!= null && !fileVList.isEmpty()){
                answer.setAnswerFileList(fileVList);
            }
        }
        //排序
        answerVoList = answerVoList.stream().sorted(Comparator.comparing(UniversityHomeworkQuestionVo::getQuestionSort)).collect(Collectors.toList());
        record.setAnswerList(answerVoList);
    }














    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    @Override
    public IPage<UniversityHomeworkRecord> findPage(IPage page, UniversityHomeworkRecordFindDTO dto){
        return getBaseMapper().findPage(page,dto);
    }

    /**
     * 删除
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(UniversityHomeworkRecord entity){
        this.getBaseMapper().deleteByIdWithFill(entity);
    }

    /**
     * 批量保存
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<UniversityHomeworkRecord> list){
        return this.saveBatch(list);
    }


    /**
     * 批量更新
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdate(List<UniversityHomeworkRecord> list){
        list.forEach(t->
                t.setUpdateTime(null)
                        .setUpdateUser(null)
        );
        return this.updateBatchById(list);
    }



    /**
     * 开始作业
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UniversityHomeworkRecordAllVo beginHomework(UniversityBeginHomeworkDTO dto) {
        String taskDetId = dto.getUserTaskDetId();

        //获取用户任务
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(taskDetId);
        //判断解锁状态
        if(det.getUnlockedState()!= null && det.getUnlockedState().equals(0)){
            throw new AppBizException("999000", "任务未解锁,无法做此操作", StringUtils.EMPTY);
        }
        //状态(0-待开始;1-进行中;2-已完成;3-未完成4-延期完成)
        if(det.getTaskStatus().equals(0)){

            //作业任务
            UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
            List<UniversityTaskHomeworkRelQuestionVo> relQuestionList = task.getTaskHomework().getTaskHomeworkRelQuestionList();


            Timestamp now = Timestamp.valueOf(LocalDateTime.now());
//            // 当前时间在任务开始和任务结束之间
//            if (det.getTaskType().equals(1) && det.getTaskType().equals(1) && !(now.after(det.getStartTime()) && now.before(det.getEndTime()))) {
//                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
//            }

            // 当前时间在任务开始和任务结束之间
            if (det.getTaskType().equals(1)){
                //未开始
                if(!now.after(det.getStartTime())){
                    SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
                }
                //过期可学
                if(det.getIsExpireStudy().equals(1)){
                }else if(!now.before(det.getEndTime())) {
                    SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
                }
            }
            UniversityHomeworkRecord record = setBeginHomework(dto, det, task, relQuestionList, now);
            //更新
            det.setTaskStatus(1).setUpdateTime(null).setUpdateUser(null)
                    .setStartUserTime(new Timestamp(System.currentTimeMillis()));
            universityUserTaskDetService.getBaseMapper().updateById(det);
            //更新总进度
            UniversityUserTaskDetUpdateVO detUpdateVO = new UniversityUserTaskDetUpdateVO();
            BeanUtils.copyProperties(det,detUpdateVO);
            universityUserTaskDetService.parentStart(detUpdateVO);
            return this.getById(record.getId());

        }else if(det.getTaskStatus().equals(1)){
            //状态(0-待开始;1-进行中;2-已完成;3-未完成4-延期完成)
            QueryWrapper<UniversityHomeworkRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(UniversityHomeworkRecord::getUserTaskDetId,dto.getUserTaskDetId());
            List<UniversityHomeworkRecord> recordList = this.baseMapper.selectList(queryWrapper);

            Timestamp now = Timestamp.valueOf(LocalDateTime.now());
            // 当前时间在任务开始和任务结束之间
            if (det.getTaskType().equals(1) && !(now.after(det.getStartTime()) && now.before(det.getEndTime())) && !det.getIsExpireStudy().equals(1)) {
                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
            }
            List<UniversityHomeworkRecord> noFinshRecordList = recordList.stream().filter(t->t.getHomeworkStatus().equals(1)).collect(Collectors.toList());
            List<UniversityHomeworkRecord> finshRecordList = recordList.stream().filter(t->t.getHomeworkStatus().equals(2)).collect(Collectors.toList());
            //有未完成的作业
            if(!noFinshRecordList.isEmpty()){
                //接返回题目信息列表
                return this.getById(noFinshRecordList.get(0).getId());
            }else{

                //作业任务
                UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());

                finshRecordList = finshRecordList.stream().sorted(Comparator.comparing(UniversityHomeworkRecord::getCreateTime).reversed()).collect(Collectors.toList());
                if(finshRecordList!= null && !finshRecordList.isEmpty()){
                    if(task.getCompleteCondition().equals(3)){
                        throw new AppBizException("999000", "任务已提交,无法做此操作", StringUtils.EMPTY);
                    }else if(task.getCompleteCondition().equals(5)){
                        for (UniversityHomeworkRecord record : finshRecordList) {
                            //批阅状态(0-待批阅;1-批阅中;2-已批阅;)
                            if(!record.getReadOverStatus().equals(2)){
                                throw new AppBizException("999000", "作业未批阅完,无法做此操作", StringUtils.EMPTY);
                            }
                        }
                    }
                }

                List<UniversityTaskHomeworkRelQuestionVo> relQuestionList = task.getTaskHomework().getTaskHomeworkRelQuestionList();
                UniversityHomeworkRecord record = setBeginHomework(dto, det, task, relQuestionList, now);
                return this.getById(record.getId());
            }
        }else if(det.getTaskStatus().equals(2)){
            //状态 2-已完成
            SysHttpResult.ERROR_UNIVERSITY_USER_HOMEWORK_FINSH.appBizException();
        }else if(det.getTaskStatus().equals(3)){
            //状态 3-未完成
            SysHttpResult.ERROR_UNIVERSITY_USER_HOMEWORK_NOT_FINSH.appBizException();
        }else if(det.getTaskStatus().equals(4)){
            //状态 4-延期完成
            SysHttpResult.ERROR_UNIVERSITY_USER_HOMEWORK_FINSH.appBizException();
        }
        return new UniversityHomeworkRecordAllVo();
    }


    private UniversityHomeworkRecord setBeginHomework(UniversityBeginHomeworkDTO dto, UniversityCourseParticipantTaskDet det, UniversityTaskVO task, List<UniversityTaskHomeworkRelQuestionVo> relQuestionList, Timestamp now) {
        //记录
        UniversityHomeworkRecord record = new UniversityHomeworkRecord();
        record.setUserTaskDetId(dto.getUserTaskDetId())
                .setQualifyScore(task.getTaskHomework().getReadOverScore()!= null?BigDecimal.valueOf(task.getTaskHomework().getReadOverScore()):null)
                .setHomeworkBeginTime(now)
                .setHomeworkStatus(1);


        this.baseMapper.insert(record);

        List<UniversityHomeworkQuestion> answerList = new ArrayList<>();
        Integer questionSort = 1;
        for (UniversityTaskHomeworkRelQuestionVo rel : relQuestionList) {
            UniversityHomeworkQuestion answer = new UniversityHomeworkQuestion();
            answer.setHomeworkRecordId(record.getId())
                    .setQuestionId(rel.getQuestionId())
                    .setQuestionSort(questionSort)
                    .setQuestionType(rel.getType())
                    .setIsNotAnswered(1);
            questionSort++;
            answerList.add(answer);
        }
        //作业题目处理
        if(!answerList.isEmpty()){
            universityHomeworkQuestionService.batchSave(answerList);
        }
        return record;
    }




    /**
     * 详情
     * @param recordId
     * @return
     */
    @Override
    public UniversityHomeworkRecordVo get(String recordId) {
        return this.baseMapper.getById(recordId);
    }



    /**
     * 结束作业
     *
     * @param recordId
     * @param endType
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endHomework(String recordId, Integer endType) {

        //作业记录
        UniversityHomeworkRecordVo record = this.baseMapper.getById(recordId);
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());

        //作业状态不是开始中,无法结束
        if(!record.getHomeworkStatus().equals(1)){
            SysHttpResult.ERROR_UNIVERSITY_USER_TASK_END_EXAM_STATUS.appBizException();
        }




        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskHomeworkVo taskHomework = task.getTaskHomework();

        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
//        if(det.getStartTime()!= null && det.getEndTime()!= null){
//            // 当前时间在任务开始和任务结束之间
//            if (det.getTaskType().equals(1) && !(now.after(det.getStartTime()) && now.before(det.getEndTime()))) {
//                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
//            }
//        }

        // 当前时间在任务开始和任务结束之间
        if (det.getTaskType().equals(1)){
            //未开始
            if(!now.after(det.getStartTime())){
                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
            }
            //过期可学
            if(det.getIsExpireStudy().equals(1)){
            }else if(!now.before(det.getEndTime()) && endType.equals(1)) {
                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
            }
        }

        //作业记录-考题列表
        List<UniversityHomeworkQuestion> questionAnswerList = universityHomeworkQuestionService.findListByReord(recordId);
        //作业记录-考题明细列表
        List<UniversityHomeworkQuestionDet> answerDetList = universityHomeworkQuestionDetService.findListByReord(recordId);
        Map<String, List<UniversityHomeworkQuestionDet>> userAnswerMap = answerDetList.stream()
                .collect(Collectors.groupingBy(UniversityHomeworkQuestionDet::getHomeworkQuestionId));



        //题目列表
        List<UniversityHomeworkSubmitQuestionVo> questionVoList = setQuestionList(recordId, record);
        Map<String, UniversityHomeworkSubmitQuestionVo> questionVoMap = questionVoList.stream()
                .collect(Collectors.toMap(UniversityHomeworkSubmitQuestionVo::getId, t -> t, (k1, k2) -> k1));

        //员工回答更新列表
        List<UniversityHomeworkQuestionDet> answerDetUpdateList = new ArrayList<>();
        List<UniversityHomeworkQuestion> answerUpdateList = new ArrayList<>();

        // 校验提交要求文本 问答题必填
        //查询提交要求是否包含文本
        List<Integer> submitReqTypeList=this.getBaseMapper().findsubmitReqTypeById(recordId);
        if (submitReqTypeList!=null && submitReqTypeList.size()>0) {
            if (submitReqTypeList.contains(1)) {
                //判断问答题 不能为空
                for (UniversityHomeworkQuestion answer : questionAnswerList) {
                    if(answer.getQuestionType().equals(6)&&StringUtils.isEmpty(answer.getAnswer())){
                        SysHttpResult.ERROR_ANSWER_IS_NOT_NULL.appBizException();
                    }
                }
            }
        }


        for (UniversityHomeworkQuestion answer : questionAnswerList) {
            //提交作业的时候问题答  或者  不答  =>不做处理
            if(answer.getQuestionType().equals(6)){
                continue;
            }
            if(answer.getIsNotAnswered()== null ||  answer.getIsNotAnswered().equals(1)){
                answer.setIsRight(0);
                //添加更新问题
                answerUpdateList.add(answer);
                continue;
            }
            UniversityHomeworkSubmitQuestionVo questionVo = questionVoMap.get(answer.getId());
            //题目分类(1-单选 2-多选 3-不定项 4-判断 5-填空 6-问答)
            if(answer.getQuestionType().equals(1)) {
                //题目分类 1-单选
                setOption(userAnswerMap, answerDetUpdateList, answer, questionVo);
            }else if(answer.getQuestionType().equals(2)) {
                //题目分类 2-多选

                setOptionList(userAnswerMap, answerDetUpdateList, answer, questionVo);
            }else if(answer.getQuestionType().equals(3)) {
                //题目分类 3-不定项
                //获取对的选项
                setOptionList(userAnswerMap, answerDetUpdateList, answer, questionVo);
            }else if(answer.getQuestionType().equals(4)) {
                //题目分类 4-判断
                if(answer.getJudgeAnswer()!= null && questionVo.getJudgeAnswer().equals(answer.getJudgeAnswer())){
                    answer.setIsRight(1);
                }else{
                    answer.setIsRight(0);
                }
            }else if(answer.getQuestionType().equals(5)) {
                //题目分类 5-填空
                setGaoFilling(userAnswerMap, answerDetUpdateList, answer, questionVo);

            }
            //添加更新问题
            answerUpdateList.add(answer);
        }


        if(!answerUpdateList.isEmpty()){
            for (UniversityHomeworkQuestion question : answerUpdateList) {
                question.setUpdateTime(null).setUpdateTime(null);
            }
            //更新问题
            universityHomeworkQuestionService.batchUpdate(answerUpdateList);
        }

        //更新问题答案明细
        if(!answerDetUpdateList.isEmpty()){
            universityHomeworkQuestionDetService.batchUpdate(answerDetUpdateList);
        }

        UniversityHomeworkRecord entity = this.baseMapper.selectById(recordId);


        //是否机批(0-否;1-是;)
        if(taskHomework.getIsPcReadOver()!= null && taskHomework.getIsPcReadOver().equals(1)){

            UniversityPcReadOverSaveDTO pcReadOver = new UniversityPcReadOverSaveDTO();
            //完成条件(1.观看时长 2.学到最后  3.提交 4.分数达标)
            if(task.getCompleteCondition().equals(3)){
                //批阅状态(0-待批阅;1-批阅中;2-已批阅;
                entity.setReadOverStatus(2);
                pcReadOver.setReadOverStatus(2);
                pcReadOver.setRecordId(recordId);
                pcReadOver.setReadOverType(1);

                //机批
                universityReadOverSpotCheckService.pcReadOverSave(pcReadOver,record.getQualifyScore(),task,det,now);
            }
            //else if(task.getCompleteCondition().equals(4)){
            //分数达标
//               if(record.getQualifyScore()!= null && record.getUserScore().compareTo(record.getQualifyScore())>=0){
//                   entity.setReadOverStatus(2);
//                   pcReadOver.setReadOverStatus(2);
//               }else{
            //   entity.setReadOverStatus(2);
            //  pcReadOver.setReadOverStatus(3);
            // }
            // }


        }else{
            //有人工评阅
            if(!taskHomework.getIsReadOver().equals(0)){
                //批阅状态(0-待批阅;1-批阅中;2-已批阅;
                entity.setReadOverStatus(0);
            }
        }




        //作业状态 2已结束
        entity.setHomeworkStatus(2);
        entity.setHomeworkEndTime(new Timestamp(System.currentTimeMillis()));
        entity.setUpdateTime(null).setUpdateUser(null);

        Integer minNum = TaskTimeUtils.betweenMinimum(entity.getHomeworkBeginTime(), entity.getHomeworkEndTime());
        entity.setUserTimeNum(minNum);
        this.updateById(entity);


        if(task.getCompleteCondition().equals(3)){
            log.info("recordId ={} 完成条件-提交任务 更新进度 ",entity.getId());
            //调用完成功能
            UniversityUserTaskDetUpdateVO detUpdateVO = new UniversityUserTaskDetUpdateVO();
            if (det.getTaskType().equals(1) && det.getIsExpireStudy().equals(1) && now.after(det.getEndTime())) {
                det.setTaskStatus(4);
            }else{
                det.setTaskStatus(2);
            }
            BeanUtils.copyProperties(det,detUpdateVO);
            detUpdateVO.setProgress(BigDecimal.valueOf(100));
            universityUserTaskDetService.taskUpdateProgressAll(detUpdateVO);
        }
        //是否添加抽检待办：0否；1是
        Integer checkUserType = 0;
        //是否机批(0-否;1-是;)
        if(taskHomework.getIsPcReadOver().equals(0)){
            //有人工评阅
            if(!taskHomework.getIsReadOver().equals(0)){
                //保存评阅人
                // 消息通知
                this.pushReviewMessage(det.getSysUserId(), taskHomework.getManualReviewList(), task.getTaskName(), record.getId(), det);
                universityReadOverSpotCheckUserService.saveUserIds(taskHomework.getManualReviewList(),1,record.getId(),det, Common.getTenantId(),Common.getUserId(), checkUserType);
            }
            //不批阅 但是 要抽检
            if(taskHomework.getIsReadOver().equals(0) && taskHomework.getIsSpotCheck().equals(1)){
                checkUserType = 1;
                //保存评阅人
                universityReadOverSpotCheckUserService.saveUserIds(taskHomework.getSpotCheckUserList(),2,record.getId(),det, Common.getTenantId(), Common.getUserId(),checkUserType);
            }
        }




    }


    // 通知批阅人
    public void pushReviewMessage(String student, List<UniversityTaskUserRelObjectVo> manualReviewList, String taskName, String recordId,
                                  UniversityCourseParticipantTaskDet det) {
        List<UniversityMessageDTO> dtoList = new ArrayList<>();

        // 获取批阅人id
        List<String> pushUserList =  universityReadOverSpotCheckUserService.getUserIds(manualReviewList,1,recordId,det,0);
        if (CollectionUtils.isEmpty(pushUserList)) return;

        String objectsName = null;
        UniversityCourseParticipantTask userTask = userTaskService.queryById(det.getParticipantTaskId());
        if (Objects.isNull(userTask)) return;

        if (det.getTaskType().equals(0)){
            // 课程
            UniversityCourse course = courseService.lambdaQuery().eq(UniversityCourse::getId, userTask.getActivityId()).one();
            objectsName = Objects.nonNull(course) ? course.getCourseTitle():"";
        }else {
            // 项目
            UniversityProject project = projectService.lambdaQuery().eq(UniversityProject::getId, userTask.getActivityId()).one();
            objectsName = Objects.nonNull(project) ? project.getProjectName():"";
        }

        String param = "homeworkRecordId=" + recordId;
        Map<String, String> extrasMap = new HashMap<>();
        extrasMap.put("homeworkRecordId", recordId);
        for (String userId : pushUserList) {
            dtoList.add(new UniversityMessageDTO().setPushSysUserId(userId)
                    .setSysUserId(student)
                    .setObjectsName(objectsName).setHomeworkName(taskName)
                    .setObjectsParameter(param).setExtrasMap(extrasMap)
            );
        }
        // 学习提醒
        messageRecordService.batchPushMessage(MessageTypeEnum.UNMARKED_WORK, dtoList);
    }



    /**
     * 设置填空
     * @param userAnswerMap
     * @param answerDetUpdateList
     * @param answer
     * @param questionVo
     */
    private void setGaoFilling(Map<String, List<UniversityHomeworkQuestionDet>> userAnswerMap, List<UniversityHomeworkQuestionDet> answerDetUpdateList, UniversityHomeworkQuestion answer, UniversityHomeworkSubmitQuestionVo questionVo) {
        //获取填空map
        Map<Integer, SysExamQuestionAnswerDetailVo>  gapFillingMap = questionVo.getSysExamQuestionAnswerDetailVos().stream()
                .collect(Collectors.toMap(SysExamQuestionAnswerDetailVo::getSort, t -> t));

        //获取回答明细
        List<UniversityHomeworkQuestionDet> userAnswerList = userAnswerMap.get(answer.getId());

        //回答正确的list
        List<Integer> answerTrueList = new ArrayList<>();

        if(userAnswerList == null){
            answer.setIsRight(0);
            return;
        }


        for (UniversityHomeworkQuestionDet answerDet : userAnswerList) {

            //获取填空题正确答案
            SysExamQuestionAnswerDetailVo sysExamQuestionAnswerDetailVo = gapFillingMap.get(answerDet.getRecordSort());
            String gapFilling = sysExamQuestionAnswerDetailVo.getGapFilling();

            //设置填空题回答
            if(StringUtils.isBlank(answerDet.getGapFilling())){
                answerDet.setOptionTrue(0);
            }else{
                List<String> trueList = Arrays.stream(gapFilling.split(";"))
                        .collect(Collectors.toList());
                if(trueList.contains(answerDet.getGapFilling())){
                    answerDet.setOptionTrue(1);
                    answerTrueList.add(answerDet.getRecordSort());
                }else{
                    answerTrueList = new ArrayList<>();
                    answerDet.setOptionTrue(0);
                }

            }
            //添加更新处理
            answerDetUpdateList.add(answerDet);
        }

        if(userAnswerList.size() == answerTrueList.size()){
            //全对
            answer.setIsRight(1);
        }else {
            //不对
            answer.setIsRight(0);
        }
    }

    private void setOptionList(Map<String, List<UniversityHomeworkQuestionDet>> userAnswerMap, List<UniversityHomeworkQuestionDet> answerDetUpdateList, UniversityHomeworkQuestion answer, UniversityHomeworkSubmitQuestionVo questionVo) {
        //获取对的选项
        List<String> isTrueOptionList = questionVo.getSysExamQuestionAnswerDetailVos().stream().filter(t -> t.getOptionTrue().equals(1))
                .map(SysExamQuestionAnswerDetailVo::getOptionName).collect(Collectors.toList());


        //获取回答明细
        List<UniversityHomeworkQuestionDet> userAnswerList = userAnswerMap.get(answer.getId());
        List<String> answerOptionList = new ArrayList<>();

        if(userAnswerList == null){
            //不对
            answer.setIsRight(0);
            return;
        }



        for (UniversityHomeworkQuestionDet answerDet : userAnswerList) {
            if(isTrueOptionList.contains(answerDet.getOptionName())){
                answerOptionList.add(answerDet.getOptionName());
                answerDet.setOptionTrue(1);
            }else{
                answerOptionList = new ArrayList<>();
                answerDet.setOptionTrue(0);
            }
            //添加更新处理
            answerDetUpdateList.add(answerDet);
        }


        if(answerOptionList.size() == isTrueOptionList.size()){
            //全对
            answer.setIsRight(1);
        }else if(!answerOptionList.isEmpty()){
            //部分
            answer.setIsRight(2);
        }else{
            //不对
            answer.setIsRight(0);
        }
    }

    private void setOption(Map<String, List<UniversityHomeworkQuestionDet>> userAnswerMap, List<UniversityHomeworkQuestionDet> answerDetUpdateList, UniversityHomeworkQuestion answer, UniversityHomeworkSubmitQuestionVo questionVo) {
        List<SysExamQuestionAnswerDetailVo> isTrueDetList = questionVo.getSysExamQuestionAnswerDetailVos().stream()
                .filter(t -> t.getOptionTrue().equals(1)).collect(Collectors.toList());
        SysExamQuestionAnswerDetailVo isTrueDetVo = isTrueDetList.get(0);

        //获取回答明细
        List<UniversityHomeworkQuestionDet> userAnswerList = userAnswerMap.get(answer.getId());

        if(userAnswerList!= null && !userAnswerList.isEmpty()){
            UniversityHomeworkQuestionDet userAnswerDet = userAnswerList.get(0);
            //设置回答正确以及分数
            if(isTrueDetVo.getOptionName().equalsIgnoreCase(userAnswerDet.getOptionName())){
                userAnswerDet.setOptionTrue(1);
                answer.setIsRight(1);
            }else{
                userAnswerDet.setOptionTrue(0);
                answer.setIsRight(0);
            }
            answerDetUpdateList.add(userAnswerDet);
        }else{
            answer.setIsRight(0);
        }

    }




    /**
     * 完成作业
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finshHomework(UniversityHomeworkReadOverSaveDTO dto) {
        String recordId = dto.getRecordId();
        //作业记录
        UniversityHomeworkRecordVo record = this.baseMapper.getById(recordId);
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());

        //考卷非待批阅状态,无法批阅
        if(!record.getReadOverStatus().equals(0)){
            SysHttpResult.ERROR_UNIVERSITY_HOMEWORK_READ_OVER_STATUS.appBizException();
        }

        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());

        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        // 当前时间在任务开始和任务结束之间
//        if (det.getTaskType().equals(1) && !(now.after(det.getStartTime()) && now.before(det.getEndTime()))) {
//            SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
//        }


//        // 当前时间在任务开始和任务结束之间
//        if (det.getTaskType().equals(1)){
//            //未开始
//            if(!now.after(det.getStartTime())){
//                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
//            }
//            //过期可学
//            if(det.getIsExpireStudy().equals(1)){
//            }else if(!now.before(det.getEndTime())) {
//                SysHttpResult.ERROR_UNIVERSITY_USER_TASK_DET_DATE.appBizException();
//            }
//        }


        UniversityHomeworkRecord entity = this.baseMapper.selectById(recordId);

        //批阅状态(0-待批阅;1-批阅中;2-已批阅;
        entity.setReadOverStatus(2)
                .setUserScore(dto.getReadOverScore())
                .setUpdateTime(null)
                .setUpdateUser(null);
        this.baseMapper.updateById(entity);

        //完成条件-审批通过
        if(task.getCompleteCondition().equals(5) && dto.getReadOverStatus().equals(2) ){
            log.info("recordId ={} 完成条件-审批通过更新进度 ",entity.getId());
            //调用完成功能
            UniversityUserTaskDetUpdateVO detUpdateVO = new UniversityUserTaskDetUpdateVO();
            if (det.getTaskType().equals(1) && det.getIsExpireStudy().equals(1) && now.after(det.getEndTime())) {
                det.setTaskStatus(4);
            }else{
                det.setTaskStatus(2);
            }
            BeanUtils.copyProperties(det,detUpdateVO);
            detUpdateVO.setProgress(BigDecimal.valueOf(100));
            universityUserTaskDetService.taskUpdateProgressAll(detUpdateVO);
        }
    }


    /**
     * 作业说明
     *
     * @param dto
     * @return
     */
    @Override
    public UniversityTaskHomeworkDescVo homeworkDesc(UniversityHomeworkDescDTO dto) {
        UniversityTaskHomeworkDescVo vo = new UniversityTaskHomeworkDescVo();

        //任务
        UniversityTaskVO task = universityTaskService.queryById(dto.getTaskId());
        vo.setTask(task);

//        List<UniversityHomeworkSubmitQuestionVo> questionVoList= universityTaskHomeworkService.findQuestionListByTaskId(dto.getTaskId());
//        vo.setQuestionList(questionVoList);
        return vo;
    }



    /**
     * 详情
     * @param recordId
     * @return
     */
    @Override
    public UniversityHomeworkRecordVo getInfo(String recordId) {
        //获取回答记录
        UniversityHomeworkRecordVo record = this.baseMapper.getById(recordId);

        //设置回答信息
        setRecord(recordId,record);
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> getHomeworkReadOver(String recordId, Integer endType, Integer isSave) {
        List<String> userIds = new ArrayList<>();
        //作业记录
        UniversityHomeworkRecordVo record = this.baseMapper.getById(recordId);
        UniversityCourseParticipantTaskDet det = universityUserTaskDetService.queryById(record.getUserTaskDetId());
        //作业任务
        UniversityTaskVO task = universityTaskService.queryById(det.getDetailId());
        UniversityTaskHomeworkVo taskHomework = task.getTaskHomework();
        //是否机批(0-否;1-是;)
        if(taskHomework.getIsPcReadOver().equals(0)){
            //有人工评阅
            if(!taskHomework.getIsReadOver().equals(0)){
                //保存评阅人
                userIds =  universityReadOverSpotCheckUserService.getUserIds(taskHomework.getManualReviewList(),1,record.getId(),det,isSave);
            }
        }
        return userIds;
    }


}




