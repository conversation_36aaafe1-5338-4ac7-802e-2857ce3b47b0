  #-Ddiscovery_conifg_peer1=127.0.0.1 -Ddiscovery_user_peer1=127.0.0.1 -Dapp_discovery_enabled=true -Dio.netty.allocator.type=unpooled
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    name: ${system_name:wxy-system-server}
  main:
    allow-bean-definition-overriding: true
  cloud:
    sentinel:
      eager: true
      transport:
        dashboard: ${sentinel_peer1:127.0.0.1:28761}
        port: ${sentinel.system.port1:7805}
        clientIp: ${sentinel_app2_ip:127.0.0.1}
      enabled: true
    nacos:
      username: ${nacos.username:nacos}
      password: ${nacos.password:nacos}
      server-addr: ${discovery_user_peer1:127.0.0.1}:${discovery_user_port1:8765}
      register-enabled: ${app_discovery_register_enabled:true}
      discovery:
        group: ${appenv:dev}
        namespace: ${application.namespace:wxy}
        enabled: ${app_discovery_enabled:false}
        metadata:
          version: ${dis_version:1.0}
          group: ${dis_group:wxy_group}
      config:
        server-addr: ${discovery_conifg_peer1:127.0.0.1}:${discovery_conifg_port1:8765}
        enabled: true
        group: ${appenv:dev}
        namespace: ${application.namespace:wxy}
        file-extension: yaml
        refresh-enabled: true
        prefix: ${spring.application.name:wxy}
        shared-configs:
          - data-id: actuator-public.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: feign-public.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: druid-public.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: mybatis-plus.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: service-public.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: redis-public.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: fastdfs.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: flyway.yaml
            group: ${appenv:dev}
            refresh: true
          - data-id: rabbitmq.yml
            group: ${appenv:dev}
            refresh: true
          - data-id: seata.yml
            group: ${appenv:dev}
            refresh: true
#          - data-id: mongodb.yaml
#            group: ${appenv:dev}
#            refresh: true
        username: ${spring.cloud.nacos.username}
        password: ${spring.cloud.nacos.password}
server:
  port: 8805

xxl:
  job:
    admin:
      addresses: http://${xxl_admin:127.0.0.1:8702}/xxl-job-admin
    accessToken: 123456
    executor:
      appname: ${spring.application.name}
      address:
      ip: ${app1_system_ip:**************}
      port: ${app1_system_port:6805}
      logpath: log/jobhandler/
      logretentiondays: 30

#========================================新的配置 添加至分割线下面==========================================================

#mybatis-plus:
  #typeEnumsPackage: com.wxy.system.server.enumerate.position



wx:
  sys:
    url: ${sys.or.code:https://simplesleep.cn:8989/miniProgram}
    param-name: userId