<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wxy.system.server.dao.university.students.UniversityCourseParticipantTaskDetDao">
    <sql id="AllColumns">
        t.id as id,
        t.sys_user_id as sysUserId,
        t.object_id as objectId,
        t.detail_id as detailId,
        t.parent_id as parentId,
        t.progress as progress,
        t.task_type as taskType,
        t.task_status as taskStatus,
        t.video_time as videoTime,
        t.start_time as startTime,
        t.end_time as endTime,
        t.create_user as createUser,
        t.create_time as createTime,
        t.update_user as updateUser,
        t.update_time as updateTime,
        t.tenant_id as tenantId
    </sql>

    <select id="findPage" resultType="com.wxy.system.server.po.university.students.UniversityCourseParticipantTaskDet">
        SELECT
        <include refid="AllColumns"/>
        FROM university_course_participant_task_det t

    </select>


    <select id="selectUnfinshTsak" resultMap="UserTaskMap">
        SELECT
            t.id as id,
            t.task_name as taskName,
            t.teach_tool as teachTool,
            t.need_teach as needTeach,
            t.teach_instruction as teachInstruction,
            t.complete_condition as completeCondition,
            t.can_optional as canOptional,
            t.duration as duration,
            t.update_progress as updateProgress,
            t.achievement_score as achievementScore,
            pt.id as taskDetId,
            pt.id as courseParticipantTaskDetId,
            pt.detail_id as detailId,
            pt.task_type as detailType,
            pt.task_status as taskStatus,
            pt.progress as progress,
            pt.video_time as videoTime,
            pt.claim_reward as claimReward,
            pt.submit_time as submitTime,
            pt.start_time as startTime,
            pt.end_time as endTime
        FROM university_course_participant_task_det pt
        INNER JOIN university_task t ON t.id = pt.detail_id AND t.is_valid = 1
        INNER JOIN university_course_participant_task task ON task.id = pt.participant_task_id AND task.is_valid = 1
        INNER JOIN university_project pj ON pj.id = task.activity_id AND pj.is_valid = 1 AND pj.project_status != 2
        WHERE pt.is_valid = 1 AND pt.teach_tool != 8 AND pt.task_status IN(0,1) AND pt.task_type = 1
              AND pt.sys_user_id = #{dto.sysUserId} AND pt.unlocked_state=1
              AND (now() BETWEEN pt.start_time AND pt.end_time)
            <if test="dto.nameKeyword != null and dto.nameKeyword != ''">
                AND t.task_name LIKE concat('%',#{dto.nameKeyword},'%')
            </if>
        ORDER BY pt.end_time
    </select>



    <select id="selectCourseByUserId"
            resultType="com.wxy.system.common.vo.university.course.UniversityStuCourseListVO">
        SELECT
            t.id AS courseId,
            t.course_no AS courseNo,
            t.course_title AS courseTitle,
            t.study_profile_id AS studyProfileId,
            pt.progress AS progress,
            pt.task_status AS taskStatus
        FROM university_course_participant_task pt
        INNER JOIN university_course t ON t.id = pt.activity_id
        WHERE t.is_valid = 1 AND pt.is_valid = 1 AND pt.task_type = 0 AND t.status = 1
              AND pt.sys_user_id = #{dto.sysUserId}
        <if test="dto.nameKeyword != null and dto.nameKeyword != ''">
            AND t.course_title LIKE concat('%',#{dto.nameKeyword},'%')
        </if>
    </select>



    <resultMap id="UserTaskMap" type="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        <result property="id" column="id" />
        <result property="taskName" column="taskName" />
        <result property="teachTool" column="teachTool" />
        <result property="needTeach" column="needTeach" />
        <result property="completeCondition" column="completeCondition" />
        <result property="canOptional" column="canOptional" />
        <result property="duration" column="duration" />
        <result property="updateProgress" column="updateProgress" />
        <result property="achievementScore" column="achievementScore" />
        <result property="taskDetId" column="taskDetId" />
        <result property="startTime" column="startTime" />
        <association property="userTaskSingleVo" javaType="com.wxy.system.common.vo.university.students.UserTaskSingleVo">
            <result property="id" column="courseParticipantTaskDetId" />
            <result property="detailId" column="detailId" />
            <result property="detailType" column="detailType" />
            <result property="taskStatus" column="taskStatus" />
            <result property="unlockedState" column="unlockedState" />
            <result property="progress" column="progress" />
            <result property="claimReward" column="claimReward" />
            <result property="videoTime" column="videoTime" />
            <result property="submitTime" column="submitTime" />
            <result property="startTime" column="startTime" />
            <result property="endTime" column="endTime" />
        </association>
    </resultMap>



    <select id="selectTsakList" resultMap="UserTaskMap">
        SELECT
            t.id as id,
            t.task_name as taskName,
            t.teach_tool as teachTool,
            t.need_teach as needTeach,
            t.complete_condition as completeCondition,
            t.can_optional as canOptional,
            t.duration as duration,
            t.update_progress as updateProgress,
            t.achievement_score as achievementScore,
            pt.ident_flag as identFlag,
            pt.ident_flag_Type as identFlagType,
            pt.id as taskDetId,
            pt.id as courseParticipantTaskDetId,
            pt.detail_id as detailId,
            pt.task_type as detailType,
            pt.task_status as taskStatus,
            pt.unlocked_state as unlockedState,
            pt.progress as progress,
            pt.video_time as videoTime,
            pt.claim_reward as claimReward,
            pt.submit_time as submitTime,
            pt.start_time as startTime,
            pt.end_time as endTime
        FROM university_course_participant_task_det pt
        INNER JOIN university_task t ON t.id = pt.detail_id
        WHERE t.is_valid = 1 AND pt.is_valid = 1 AND pt.teach_tool != 8
              AND pt.sys_user_id = #{sysUserId}
            <if test="taskDetIds != null and taskDetIds.size() > 0">
                AND pt.id IN
                <foreach collection="taskDetIds" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="projectId != null and projectId != ''">
                AND pt.participant_task_id IN(
                        SELECT id FROM university_course_participant_task WHERE activity_id = #{projectId} AND sys_user_id = #{sysUserId}
                    )
            </if>
    </select>



    <select id="countUnfinishedCourseTask" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM university_course_participant_task_det pt
        INNER JOIN university_course_participant_task t ON t.id = pt.participant_task_id AND t.is_valid = 1
        INNER JOIN university_course c ON c.id = t.activity_id AND c.is_valid = 1
        WHERE pt.is_valid = 1 AND pt.task_status IN(0,1) AND pt.task_type = 0
        AND pt.sys_user_id = #{dto.sysUserId}
        <if test="dto.nameKeyword != null and dto.nameKeyword != ''">
            AND c.course_title LIKE concat('%',#{dto.nameKeyword},'%')
        </if>
    </select>
    <select id="processingList" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        WITH UserTasks AS (
            SELECT ru.record_id, ru.record_type
            FROM university_read_over_spot_check_user ru
            WHERE ru.sys_user_id = #{userId}
              AND ru.record_type IN (1, 2)
        ),
             SubmissionDetails AS (
                 SELECT
                     'Exam' AS submission_origin,
                     et.id AS record_id,
                     et.read_over_status,
                     et.test_paper_status AS submission_status,
                     det.id AS det_id,
                     det.sys_user_id,
                     det.end_user_time,
                     det.loop_date,
                     det.detail_id,
                     de.activity_id
                 FROM university_test_paper_record et
                          JOIN university_course_participant_task_det det ON det.id = et.user_task_det_id AND det.is_valid = 1
                          JOIN university_course_participant_task de ON de.id = det.participant_task_id AND de.is_valid = 1
                 WHERE et.is_valid = 1
                   AND et.test_paper_status = 2
                   AND det.end_user_time >= DATE_SUB(NOW(), INTERVAL 31 DAY)

                 UNION ALL

                 SELECT
                     'Homework' AS submission_origin,
                     hr.id AS record_id,
                     hr.read_over_status,
                     hr.homework_status AS submission_status,
                     det.id AS det_id,
                     det.sys_user_id,
                     det.end_user_time,
                     det.loop_date,
                     det.detail_id,
                     de.activity_id
                 FROM university_homework_record hr
                          JOIN university_course_participant_task_det det ON det.id = hr.user_task_det_id AND det.is_valid = 1
                          JOIN university_course_participant_task de ON de.id = det.participant_task_id AND de.is_valid = 1
                 WHERE hr.is_valid = 1
                   AND hr.homework_status = 2
                   AND det.end_user_time >= DATE_SUB(NOW(), INTERVAL 31 DAY)
             ),
             RankedResults AS (
                 SELECT
                     t.id ,
                     ut.record_type,
                     pr.spot_check_pass_rate AS spotCheckPassRate,
                     pr.project_scheduling AS projectScheduling,
                     sd.end_user_time AS submitTime,
                     sysu.user_name AS userName,
                     t.task_name AS taskName,
                     t.teach_tool AS teachTool,
                     sd.activity_id AS activityId,
                     sd.loop_date AS loopDate,
                     ut.record_id AS recordId,
                     ROW_NUMBER() OVER (PARTITION BY t.id, ut.record_type ORDER BY ut.record_id) as rn
                 FROM UserTasks ut
                          JOIN SubmissionDetails sd ON ut.record_id = sd.record_id
                          JOIN university_task t ON sd.detail_id = t.id AND t.is_valid = 1
                          LEFT JOIN sys_user sysu ON sysu.id = sd.sys_user_id
                          LEFT JOIN university_project pr ON sd.activity_id = pr.id AND pr.project_status != 2
            LEFT JOIN university_course uc ON sd.activity_id = uc.id AND uc.status != 2 AND uc.is_valid = 1
            LEFT JOIN university_read_over_spot_check ro ON ut.record_id = ro.record_id AND ut.record_type = 1 AND ro.read_over_user = #{userId}
            LEFT JOIN university_spot_check sc ON ut.record_id = sc.record_id AND ut.record_type = 2 AND sc.spot_check_user = #{userId}
        WHERE
            (pr.id IS NOT NULL OR uc.id IS NOT NULL)
          AND (
            (ut.record_type = 1 AND ro.id IS NULL AND sd.read_over_status = 0)
           OR
            (ut.record_type = 2 AND sc.id IS NULL)
            )
          AND (
            NOT (ut.record_type = 1 AND sd.submission_origin = 'Homework')
           OR sysu.tenant_id = '1'
           OR sysu.id IS NULL
            )
            )
        SELECT
            id,
            record_type,
            spotCheckPassRate,
            projectScheduling,
            submitTime,
            userName,
            taskName,
            teachTool,
            activityId,
            loopDate,
            recordId
        FROM RankedResults
        WHERE record_type = 1 OR rn = 1
    </select>

    <!-- 查询用户的待处理记录ID列表（近31天） -->
    <select id="findProcessingRecords" resultType="com.wxy.system.common.dto.university.task.ProcessingRecordDTO">
        SELECT ru.record_id as recordId, ru.record_type as recordType
        FROM university_read_over_spot_check_user ru
        WHERE ru.sys_user_id = #{userId}
          AND ru.record_type IN (1, 2)
    </select>

    <!-- 根据记录ID批量查询考试详细信息（近31天） -->
    <select id="findExamDetailsByRecordIds" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT
            t.id,
            1 as recordType,
            pr.spot_check_pass_rate AS spotCheckPassRate,
            pr.project_scheduling AS projectScheduling,
            det.end_user_time AS submitTime,
            sysu.user_name AS userName,
            t.task_name AS taskName,
            t.teach_tool AS teachTool,
            de.activity_id AS activityId,
            det.loop_date AS loopDate,
            et.id AS recordId
        FROM university_test_paper_record et
                 JOIN university_course_participant_task_det det ON det.id = et.user_task_det_id AND det.is_valid = 1
                 JOIN university_course_participant_task de ON de.id = det.participant_task_id AND de.is_valid = 1
                 JOIN university_task t ON det.detail_id = t.id AND t.is_valid = 1
                 LEFT JOIN sys_user sysu ON sysu.id = det.sys_user_id
                 LEFT JOIN university_project pr ON de.activity_id = pr.id AND pr.project_status != 2
                 LEFT JOIN university_course uc ON de.activity_id = uc.id AND uc.status != 2 AND uc.is_valid = 1
                 LEFT JOIN university_read_over_spot_check ro ON et.id = ro.record_id AND ro.read_over_user = #{userId}
        WHERE et.is_valid = 1
          AND et.test_paper_status = 2
          AND det.start_time >= DATE_SUB(NOW(), INTERVAL 31 DAY)
          AND et.id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
          AND (pr.id IS NOT NULL OR uc.id IS NOT NULL)
          AND ro.id IS NULL
          AND et.read_over_status = 0
    </select>

    <!-- 查询用户的待批阅任务ID列表 -->
    <select id="findPendingReadTaskIds" resultType="java.lang.String">
        SELECT DISTINCT srt.task_id
        FROM university_spot_read_task srt
        WHERE srt.user_id = #{userId}
          AND srt.status = 0
          AND srt.is_valid = 1
    </select>

    <!-- 根据任务ID查询批阅明细（作业） -->
    <select id="findHomeworkReadDetails" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT DISTINCT
            t.id as id,
            t.task_name as taskName,
            t.teach_tool as teachTool,
            hr.id as recordId,
            '1' as recordType,
            u.user_name as userName,
            det.submit_time as submitTime,
            pt.activity_id as activityId,
            det.loop_date as loopDate,
            CASE
                WHEN p.id IS NOT NULL THEN p.project_scheduling
                ELSE NULL
            END as projectScheduling
        FROM university_homework_record hr
        INNER JOIN university_course_participant_task_det det ON hr.user_task_det_id = det.id
        INNER JOIN university_course_participant_task pt ON det.participant_task_id = pt.id
        INNER JOIN university_task t ON det.detail_id = t.id
        INNER JOIN sys_user u ON pt.sys_user_id = u.id
        LEFT JOIN university_project p ON pt.activity_id = p.id AND p.is_valid = 1
        LEFT JOIN university_course c ON pt.activity_id = c.id AND c.is_valid = 1
        WHERE t.id = #{taskId}
          AND hr.is_valid = 1
          AND det.is_valid = 1
          AND pt.is_valid = 1
          AND t.is_valid = 1
          AND hr.read_over_status = 0
          AND (p.project_status = 1 OR c.status = 1)
    </select>

    <!-- 根据任务ID查询批阅明细（考试） -->
    <select id="findExamReadDetails" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT DISTINCT
            t.id as id,
            t.task_name as taskName,
            t.teach_tool as teachTool,
            tr.id as recordId,
            '1' as recordType,
            det.submit_time as submitTime,
            u.user_name as userName,
            pt.activity_id as activityId,
            det.loop_date as loopDate,
            CASE
                WHEN p.id IS NOT NULL THEN p.project_scheduling
                ELSE NULL
            END as projectScheduling
        FROM university_test_paper_record tr
        INNER JOIN university_course_participant_task_det det ON tr.user_task_det_id = det.id
        INNER JOIN university_course_participant_task pt ON det.participant_task_id = pt.id
        INNER JOIN university_task t ON det.detail_id = t.id
        INNER JOIN sys_user u ON pt.sys_user_id = u.id
        LEFT JOIN university_project p ON pt.activity_id = p.id AND p.is_valid = 1
        LEFT JOIN university_course c ON pt.activity_id = c.id AND c.is_valid = 1
        WHERE t.id = #{taskId}
          AND tr.is_valid = 1
          AND det.is_valid = 1
          AND pt.is_valid = 1
          AND t.is_valid = 1
          AND tr.read_over_status = 0
          AND (p.project_status = 1 OR c.status = 1)
    </select>

    <!-- 查询用户的待抽检任务（只返回任务名称） -->
    <select id="findSpotCheckTasks" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT DISTINCT
            t.id as id,
            t.task_name as taskName,
            '2' as recordType
        FROM university_spot_check_task sct
        INNER JOIN university_task t ON sct.task_id = t.id
        WHERE sct.user_id = #{userId}
          AND sct.status = 0
          AND sct.is_valid = 1
          AND t.is_valid = 1
    </select>

    <!-- 根据记录ID批量查询作业详细信息（近31天） - 这是抽检逻辑 -->
    <select id="findHomeworkDetailsByRecordIds" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT
            t.id,
            2 as recordType,
            pr.spot_check_pass_rate AS spotCheckPassRate,
            pr.project_scheduling AS projectScheduling,
            det.end_user_time AS submitTime,
            sysu.user_name AS userName,
            t.task_name AS taskName,
            t.teach_tool AS teachTool,
            de.activity_id AS activityId,
            det.loop_date AS loopDate,
            hr.id AS recordId
        FROM university_homework_record hr
                 JOIN university_course_participant_task_det det ON det.id = hr.user_task_det_id AND det.is_valid = 1
                 JOIN university_course_participant_task de ON de.id = det.participant_task_id AND de.is_valid = 1
                 JOIN university_task t ON det.detail_id = t.id AND t.is_valid = 1
                 LEFT JOIN sys_user sysu ON sysu.id = det.sys_user_id
                 LEFT JOIN university_project pr ON de.activity_id = pr.id AND pr.project_status != 2
                 LEFT JOIN university_course uc ON de.activity_id = uc.id AND uc.status != 2 AND uc.is_valid = 1
                 LEFT JOIN university_spot_check sc ON hr.id = sc.record_id AND sc.spot_check_user = #{userId}
        WHERE hr.is_valid = 1
          AND hr.homework_status = 2
          AND det.start_time >= DATE_SUB(NOW(), INTERVAL 31 DAY)
          AND hr.id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
          AND (pr.id IS NOT NULL OR uc.id IS NOT NULL)
          AND sc.id IS NULL
    </select>

    <!-- 根据记录ID批量查询作业批阅详细信息（近31天） - 这是批阅逻辑 -->
    <select id="findHomeworkReviewDetailsByRecordIds" resultType="com.wxy.system.common.vo.university.task.UniversityChildTaskVO">
        SELECT
            t.id,
            1 as recordType,
            pr.spot_check_pass_rate AS spotCheckPassRate,
            pr.project_scheduling AS projectScheduling,
            det.end_user_time AS submitTime,
            sysu.user_name AS userName,
            t.task_name AS taskName,
            t.teach_tool AS teachTool,
            de.activity_id AS activityId,
            det.loop_date AS loopDate,
            hr.id AS recordId
        FROM university_homework_record hr
                 JOIN university_course_participant_task_det det ON det.id = hr.user_task_det_id AND det.is_valid = 1
                 JOIN university_course_participant_task de ON de.id = det.participant_task_id AND de.is_valid = 1
                 JOIN university_task t ON det.detail_id = t.id AND t.is_valid = 1
                 LEFT JOIN sys_user sysu ON sysu.id = det.sys_user_id
                 LEFT JOIN university_project pr ON de.activity_id = pr.id AND pr.project_status != 2
                 LEFT JOIN university_course uc ON de.activity_id = uc.id AND uc.status != 2 AND uc.is_valid = 1
                 LEFT JOIN university_read_over_spot_check ro ON hr.id = ro.record_id AND ro.read_over_user = #{userId}
        WHERE hr.is_valid = 1
          AND hr.homework_status = 2
          AND det.start_time >= DATE_SUB(NOW(), INTERVAL 31 DAY)
          AND hr.id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
          AND (pr.id IS NOT NULL OR uc.id IS NOT NULL)
          AND ro.id IS NULL
          AND hr.read_over_status = 0
          AND (sysu.tenant_id = '1' OR sysu.id IS NULL)
    </select>

</mapper>