package com.wxy.system.common.dto.university.study;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class StudyTaskDTO implements Serializable {


    private static final long serialVersionUID = 7127669342823106410L;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String sysUserId;

    @ApiModelProperty("待办Id")
    private String readId;

    /**
     * 项目|任务|课程 名称
     */
    @ApiModelProperty("项目|任务|课程 名称")
    private String nameKeyword;
}
