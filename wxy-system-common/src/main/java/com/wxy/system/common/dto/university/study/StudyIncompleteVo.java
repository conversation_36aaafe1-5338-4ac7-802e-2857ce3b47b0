package com.wxy.system.common.dto.university.study;

import com.wxy.system.common.vo.university.task.UniversityChildTaskVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
@Accessors(chain = true)
@Data
public class StudyIncompleteVo implements Serializable {


    private static final long serialVersionUID = 2001269077880804789L;

    @ApiModelProperty("待完成任务列表")
    private List<UniversityChildTaskVO> unfinishedTaskList = new ArrayList<>();

    @ApiModelProperty("待批阅id集合")
    private List<String> readIdList;

}
